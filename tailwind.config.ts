import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // BuildLabz Color Palette
        background: "#000000",
        foreground: "#ffffff",
        primary: {
          50: "#f0fdf4",
          100: "#dcfce7",
          200: "#bbf7d0",
          300: "#86efac",
          400: "#4ade80",
          500: "#00FF9E", // Electric Neon Green
          600: "#16a34a",
          700: "#15803d",
          800: "#166534",
          900: "#14532d",
        },
        dark: {
          base: "#000000", // Pure Black
          green: {
            deep: "#0c2f27", // <PERSON>
            muted: "#1a4035", // Muted Forest
            dark: "#295247", // Dark Teal Green
          }
        },
        text: {
          primary: "#ffffff", // <PERSON> White
          secondary: "#cfcfcf", // Subtle Gray
        },
        accent: "#00FF9E", // Electric Neon Green
      },
      fontFamily: {
        'orbitron': ['Orbitron', 'sans-serif'],
        'share-tech': ['Share Tech Mono', 'monospace'],
        'titillium': ['Titillium Web', 'sans-serif'],
      },
      animation: {
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite alternate',
        'flicker': 'flicker 1.5s ease-in-out infinite alternate',
        'blob': 'blob 7s infinite',
        'float': 'float 3s ease-in-out infinite',
        'spark': 'spark 0.3s ease-out',
      },
      keyframes: {
        'pulse-glow': {
          '0%': { 
            boxShadow: '0 0 5px #00FF9E, 0 0 10px #00FF9E, 0 0 15px #00FF9E',
          },
          '100%': { 
            boxShadow: '0 0 10px #00FF9E, 0 0 20px #00FF9E, 0 0 30px #00FF9E',
          },
        },
        'flicker': {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.8' },
        },
        'blob': {
          '0%': { transform: 'translate(0px, 0px) scale(1)' },
          '33%': { transform: 'translate(30px, -50px) scale(1.1)' },
          '66%': { transform: 'translate(-20px, 20px) scale(0.9)' },
          '100%': { transform: 'translate(0px, 0px) scale(1)' },
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'spark': {
          '0%': { transform: 'scale(0) rotate(0deg)', opacity: '1' },
          '100%': { transform: 'scale(1) rotate(180deg)', opacity: '0' },
        },
      },
      backdropBlur: {
        'xs': '2px',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'grid-pattern': 'linear-gradient(rgba(0, 255, 158, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 255, 158, 0.1) 1px, transparent 1px)',
      },
    },
  },
  plugins: [],
};
export default config;

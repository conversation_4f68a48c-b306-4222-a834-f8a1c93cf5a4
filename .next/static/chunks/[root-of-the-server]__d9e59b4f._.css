/* [next]/internal/font/google/orbitron_5ca61691.module.css [app-client] (css) */
@font-face {
  font-family: Orbitron;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/yMJRMIlzdpvBhQQL_Qq7dy1biN15-s.p.8182b099.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Orbitron;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/yMJRMIlzdpvBhQQL_Qq7dy1biN15-s.p.8182b099.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Orbitron;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/yMJRMIlzdpvBhQQL_Qq7dy1biN15-s.p.8182b099.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Orbitron;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/yMJRMIlzdpvBhQQL_Qq7dy1biN15-s.p.8182b099.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Orbitron;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/yMJRMIlzdpvBhQQL_Qq7dy1biN15-s.p.8182b099.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Orbitron;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/yMJRMIlzdpvBhQQL_Qq7dy1biN15-s.p.8182b099.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Orbitron Fallback;
  src: local(Arial);
  ascent-override: 81.5%;
  descent-override: 19.59%;
  line-gap-override: 0.0%;
  size-adjust: 124.05%;
}

.orbitron_5ca61691-module__6Q6cIq__className {
  font-family: Orbitron, Orbitron Fallback;
  font-style: normal;
}

.orbitron_5ca61691-module__6Q6cIq__variable {
  --font-orbitron: "Orbitron", "Orbitron Fallback";
}


/* [next]/internal/font/google/share_tech_mono_47be315f.module.css [app-client] (css) */
@font-face {
  font-family: Share Tech Mono;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/J7aHnp1uDWRBEqV98dVQztYldFcLowEFA87Heg-s.p.2a5208f2.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Share Tech Mono Fallback;
  src: local(Arial);
  ascent-override: 73.06%;
  descent-override: 19.98%;
  line-gap-override: 0.0%;
  size-adjust: 121.13%;
}

.share_tech_mono_47be315f-module__QSln4a__className {
  font-family: Share Tech Mono, Share Tech Mono Fallback;
  font-style: normal;
  font-weight: 400;
}

.share_tech_mono_47be315f-module__QSln4a__variable {
  --font-share-tech: "Share Tech Mono", "Share Tech Mono Fallback";
}


/* [next]/internal/font/google/titillium_web_c78ac215.module.css [app-client] (css) */
@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffGjEGIVzY5abuWIGxA-s.218fe448.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffGjEGItzY5abuWI-s.p.20f9c4f5.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NaPecZTIAOhVxoMyOr9n_E7fdM3mDaZRbryhsA-s.13dacb59.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NaPecZTIAOhVxoMyOr9n_E7fdMPmDaZRbrw-s.p.87b12685.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffBzCGIVzY5abuWIGxA-s.b0d0f2b4.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffBzCGItzY5abuWI-s.p.6c7f782a.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffHjDGIVzY5abuWIGxA-s.b3cd378c.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffHjDGItzY5abuWI-s.p.dc078ea4.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Titillium Web Fallback;
  src: local(Arial);
  ascent-override: 119.97%;
  descent-override: 41.09%;
  line-gap-override: 0.0%;
  size-adjust: 94.44%;
}

.titillium_web_c78ac215-module__WGs5HG__className {
  font-family: Titillium Web, Titillium Web Fallback;
  font-style: normal;
}

.titillium_web_c78ac215-module__WGs5HG__variable {
  --font-titillium: "Titillium Web", "Titillium Web Fallback";
}


/* [project]/src/app/globals.css [app-client] (css) */
/* unparseable [project]/src/app/globals.css [app-client] (css) */

/*# sourceMappingURL=%5Broot-of-the-server%5D__d9e59b4f._.css.map*/
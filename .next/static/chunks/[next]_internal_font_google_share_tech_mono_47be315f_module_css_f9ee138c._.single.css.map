{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/share_tech_mono_47be315f.module.css"], "sourcesContent": ["/* latin */\n@font-face {\n  font-family: 'Share Tech Mono';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/sharetechmono/v15/J7aHnp1uDWRBEqV98dVQztYldFcLowEFA87Heg.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Share Tech Mono Fallback';\n    src: local(\"Arial\");\n    ascent-override: 73.06%;\ndescent-override: 19.98%;\nline-gap-override: 0.00%;\nsize-adjust: 121.13%;\n\n}\n.className {\n    font-family: 'Share Tech Mono', 'Share Tech Mono Fallback';\n    font-weight: 400;\nfont-style: normal;\n\n}\n.variable {\n    --font-share-tech: 'Share Tech Mono', 'Share Tech Mono Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;AAMA", "ignoreList": [0]}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/app/not-found.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { <PERSON>, ArrowLeft, Zap, AlertTriangle } from \"lucide-react\"\nimport Link from \"next/link\"\nimport { Button } from \"@/components/ui/button\"\nimport { BlobBackground } from \"@/components/animations/blob-background\"\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n      <BlobBackground count={2} className=\"opacity-20\" />\n      \n      {/* Grid background */}\n      <div className=\"absolute inset-0 grid-bg opacity-10\" />\n      \n      {/* Floating sparks */}\n      {Array.from({ length: 10 }).map((_, i) => (\n        <motion.div\n          key={i}\n          className=\"absolute w-1 h-1 bg-primary-500 rounded-full\"\n          style={{\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`,\n          }}\n          animate={{\n            opacity: [0, 1, 0],\n            scale: [0, 1, 0],\n          }}\n          transition={{\n            duration: 2,\n            repeat: Infinity,\n            delay: Math.random() * 2,\n          }}\n        />\n      ))}\n\n      <div className=\"relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        {/* Frankenstein Robot Illustration */}\n        <motion.div\n          className=\"mb-8\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.6 }}\n        >\n          <div className=\"relative inline-block\">\n            {/* Robot Body */}\n            <div className=\"w-32 h-40 bg-dark-green-deep border-2 border-primary-500 rounded-lg mx-auto relative\">\n              {/* Robot Head */}\n              <div className=\"w-24 h-24 bg-dark-green-muted border-2 border-primary-500 rounded-lg absolute -top-12 left-1/2 transform -translate-x-1/2\">\n                {/* Eyes */}\n                <div className=\"flex justify-center space-x-3 mt-4\">\n                  <motion.div\n                    className=\"w-3 h-3 bg-primary-500 rounded-full\"\n                    animate={{ opacity: [1, 0.3, 1] }}\n                    transition={{ duration: 2, repeat: Infinity }}\n                  />\n                  <motion.div\n                    className=\"w-3 h-3 bg-red-500 rounded-full\"\n                    animate={{ opacity: [0.3, 1, 0.3] }}\n                    transition={{ duration: 2, repeat: Infinity, delay: 1 }}\n                  />\n                </div>\n                {/* Mouth */}\n                <div className=\"w-8 h-1 bg-primary-500 rounded-full mx-auto mt-3\" />\n                \n                {/* Antenna */}\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                  <div className=\"w-0.5 h-4 bg-primary-500\" />\n                  <motion.div\n                    className=\"w-2 h-2 bg-primary-500 rounded-full -mt-1 mx-auto\"\n                    animate={{ scale: [1, 1.5, 1] }}\n                    transition={{ duration: 1, repeat: Infinity }}\n                  />\n                </div>\n              </div>\n\n              {/* Body Details */}\n              <div className=\"pt-16 px-4\">\n                <div className=\"w-full h-2 bg-primary-500/30 rounded mb-2\" />\n                <div className=\"w-3/4 h-2 bg-primary-500/30 rounded mx-auto mb-2\" />\n                <div className=\"w-1/2 h-2 bg-primary-500/30 rounded mx-auto\" />\n              </div>\n\n              {/* Arms */}\n              <div className=\"absolute top-20 -left-6 w-6 h-12 bg-dark-green-muted border-2 border-primary-500 rounded\" />\n              <div className=\"absolute top-20 -right-6 w-6 h-12 bg-dark-green-muted border-2 border-primary-500 rounded\" />\n              \n              {/* Holding wires */}\n              <motion.div\n                className=\"absolute top-24 -right-8\"\n                animate={{ rotate: [0, 10, -10, 0] }}\n                transition={{ duration: 2, repeat: Infinity }}\n              >\n                <div className=\"w-8 h-0.5 bg-primary-500\" />\n                <div className=\"w-6 h-0.5 bg-red-500 mt-1\" />\n                <div className=\"w-4 h-0.5 bg-blue-500 mt-1\" />\n              </motion.div>\n            </div>\n\n            {/* Sparks */}\n            <motion.div\n              className=\"absolute top-0 right-0\"\n              animate={{\n                opacity: [0, 1, 0],\n                scale: [0, 1, 0],\n              }}\n              transition={{\n                duration: 0.5,\n                repeat: Infinity,\n                repeatDelay: 2,\n              }}\n            >\n              <Zap className=\"w-6 h-6 text-primary-500\" />\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Error Message */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n        >\n          <h1 className=\"text-6xl md:text-8xl font-bold font-orbitron mb-4\">\n            <span className=\"text-primary-500\">4</span>\n            <span className=\"text-red-500\">0</span>\n            <span className=\"text-primary-500\">4</span>\n          </h1>\n          \n          <h2 className=\"text-2xl md:text-3xl font-bold font-orbitron mb-4\">\n            Page Short-Circuited\n          </h2>\n          \n          <p className=\"text-lg text-text-secondary mb-8 max-w-2xl mx-auto\">\n            Looks like our Frankenstein robot is having some technical difficulties. \n            The page you're looking for seems to have been zapped out of existence!\n          </p>\n\n          <motion.div\n            className=\"flex items-center justify-center space-x-2 mb-8\"\n            animate={{ opacity: [1, 0.5, 1] }}\n            transition={{ duration: 2, repeat: Infinity }}\n          >\n            <AlertTriangle className=\"w-5 h-5 text-yellow-500\" />\n            <span className=\"text-yellow-500 font-mono text-sm\">\n              ERROR: Page not found in the lab database\n            </span>\n          </motion.div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Button variant=\"magnetic\" size=\"lg\" asChild>\n              <Link href=\"/\">\n                <Home className=\"w-5 h-5 mr-2\" />\n                Back to Lab\n              </Link>\n            </Button>\n            \n            <Button variant=\"outline\" size=\"lg\" onClick={() => window.history.back()}>\n              <ArrowLeft className=\"w-5 h-5 mr-2\" />\n              Go Back\n            </Button>\n          </div>\n\n          {/* Fun fact */}\n          <motion.div\n            className=\"mt-12 p-4 bg-dark-green-deep/50 backdrop-blur-sm border border-primary-500/20 rounded-lg max-w-md mx-auto\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.8 }}\n          >\n            <p className=\"text-sm text-text-secondary\">\n              <span className=\"text-primary-500 font-semibold\">Fun Fact:</span> While you're here, \n              our robot is busy fixing the circuits. Maybe check out our{\" \"}\n              <Link href=\"/projects\" className=\"text-primary-500 hover:underline\">\n                latest projects\n              </Link>{\" \"}\n              instead?\n            </p>\n          </motion.div>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yJAAA,CAAA,iBAAc;gBAAC,OAAO;gBAAG,WAAU;;;;;;0BAGpC,6LAAC;gBAAI,WAAU;;;;;;YAGd,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wBAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oBAChC;oBACA,SAAS;wBACP,SAAS;4BAAC;4BAAG;4BAAG;yBAAE;wBAClB,OAAO;4BAAC;4BAAG;4BAAG;yBAAE;oBAClB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,OAAO,KAAK,MAAM,KAAK;oBACzB;mBAdK;;;;;0BAkBT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,SAAS;oEAAC;oEAAG;oEAAK;iEAAE;4DAAC;4DAChC,YAAY;gEAAE,UAAU;gEAAG,QAAQ;4DAAS;;;;;;sEAE9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,SAAS;oEAAC;oEAAK;oEAAG;iEAAI;4DAAC;4DAClC,YAAY;gEAAE,UAAU;gEAAG,QAAQ;gEAAU,OAAO;4DAAE;;;;;;;;;;;;8DAI1D,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,OAAO;oEAAC;oEAAG;oEAAK;iEAAE;4DAAC;4DAC9B,YAAY;gEAAE,UAAU;gEAAG,QAAQ;4DAAS;;;;;;;;;;;;;;;;;;sDAMlD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAIjB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,QAAQ;oDAAC;oDAAG;oDAAI,CAAC;oDAAI;iDAAE;4CAAC;4CACnC,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;;8DAE5C,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAKnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCACP,SAAS;4CAAC;4CAAG;4CAAG;yCAAE;wCAClB,OAAO;4CAAC;4CAAG;4CAAG;yCAAE;oCAClB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,aAAa;oCACf;8CAEA,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAe;;;;;;kDAC/B,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;0CAGrC,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,6LAAC;gCAAE,WAAU;0CAAqD;;;;;;0CAKlE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;wCAAC;wCAAG;wCAAK;qCAAE;gCAAC;gCAChC,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;;kDAE5C,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAMtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAW,MAAK;wCAAK,OAAO;kDAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,6LAAC,sMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAKrC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;;0DACpE,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAM1C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CAExC,cAAA,6LAAC;oCAAE,WAAU;;sDACX,6LAAC;4CAAK,WAAU;sDAAiC;;;;;;wCAAgB;wCACN;sDAC3D,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;sDAAmC;;;;;;wCAE5D;wCAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B;KAjLwB", "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "file": "house.js", "sources": ["file:///Users/<USER>/Desktop/clones/labs/node_modules/lucide-react/src/icons/house.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8', key: '5wwlr5' }],\n  [\n    'path',\n    {\n      d: 'M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',\n      key: '1d0kgt',\n    },\n  ],\n];\n\n/**\n * @component @name House\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjF2LThhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjgiIC8+CiAgPHBhdGggZD0iTTMgMTBhMiAyIDAgMCAxIC43MDktMS41MjhsNy01Ljk5OWEyIDIgMCAwIDEgMi41ODIgMGw3IDUuOTk5QTIgMiAwIDAgMSAyMSAxMHY5YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/house\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst House = createLucideIcon('house', __iconNode);\n\nexport default House;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3E;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///Users/<USER>/Desktop/clones/labs/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "file": "triangle-alert.js", "sources": ["file:///Users/<USER>/Desktop/clones/labs/node_modules/lucide-react/src/icons/triangle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3',\n      key: 'wmoenq',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n];\n\n/**\n * @component @name TriangleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleAlert = createLucideIcon('triangle-alert', __iconNode);\n\nexport default TriangleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
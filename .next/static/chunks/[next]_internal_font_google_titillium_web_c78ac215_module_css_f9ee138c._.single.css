/* [next]/internal/font/google/titillium_web_c78ac215.module.css [app-client] (css) */
@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffGjEGIVzY5abuWIGxA-s.6a2bf56c.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffGjEGItzY5abuWI-s.p.04720426.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NaPecZTIAOhVxoMyOr9n_E7fdM3mDaZRbryhsA-s.904f1139.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NaPecZTIAOhVxoMyOr9n_E7fdMPmDaZRbrw-s.p.9f6057fd.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffBzCGIVzY5abuWIGxA-s.a169daec.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffBzCGItzY5abuWI-s.p.641932d2.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffHjDGIVzY5abuWIGxA-s.16fa657d.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Titillium Web;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NaPDcZTIAOhVxoMyOr9n_E7ffHjDGItzY5abuWI-s.p.c6b45775.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Titillium Web Fallback;
  src: local(Arial);
  ascent-override: 119.97%;
  descent-override: 41.09%;
  line-gap-override: 0.0%;
  size-adjust: 94.44%;
}

.titillium_web_c78ac215-module__WGs5HG__className {
  font-family: Titillium Web, Titillium Web Fallback;
  font-style: normal;
}

.titillium_web_c78ac215-module__WGs5HG__variable {
  --font-titillium: "Titillium Web", "Titillium Web Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_titillium_web_c78ac215_module_css_f9ee138c._.single.css.map*/
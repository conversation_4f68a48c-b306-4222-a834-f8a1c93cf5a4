{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/sections/hero-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Rocket } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { BlobBackground } from \"@/components/animations/blob-background\"\nimport { fadeInUp, fadeInLeft, fadeInRight, staggerContainer, staggerItem } from \"@/lib/utils\"\n\nexport function HeroSection() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Elements */}\n      <BlobBackground count={4} className=\"opacity-30\" />\n      \n      {/* Grid Background */}\n      <div className=\"absolute inset-0 grid-bg opacity-10\" />\n      \n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <motion.div\n          className=\"text-center\"\n          variants={staggerContainer}\n          initial=\"initial\"\n          animate=\"animate\"\n        >\n          {/* Badge */}\n          <motion.div\n            variants={staggerItem}\n            className=\"inline-flex items-center space-x-2 bg-dark-green-deep/50 backdrop-blur-sm border border-primary-500/30 rounded-full px-4 py-2 mb-8\"\n          >\n            <Zap className=\"w-4 h-4 text-primary-500\" />\n            <span className=\"text-sm font-medium text-primary-500\">\n              Web3 Development Studio\n            </span>\n          </motion.div>\n\n          {/* Main Heading */}\n          <motion.h1\n            variants={staggerItem}\n            className=\"text-4xl md:text-6xl lg:text-7xl font-bold font-orbitron mb-6 leading-tight\"\n          >\n            <span className=\"text-glow\">We forge the</span>\n            <br />\n            <span className=\"text-primary-500\">future of Web3</span>\n          </motion.h1>\n\n          {/* Tagline */}\n          <motion.p\n            variants={staggerItem}\n            className=\"text-xl md:text-2xl text-text-secondary mb-8 max-w-3xl mx-auto\"\n          >\n            One block at a time. From idea to reality in{\" \"}\n            <span className=\"text-primary-500 font-semibold\">weeks, not months</span>.\n          </motion.p>\n\n          {/* Feature Pills */}\n          <motion.div\n            variants={staggerItem}\n            className=\"flex flex-wrap justify-center gap-4 mb-12\"\n          >\n            {[\n              { icon: Code, text: \"DApp Development\" },\n              { icon: Zap, text: \"Smart Contracts\" },\n              { icon: Rocket, text: \"Rapid Deployment\" },\n            ].map((feature, index) => (\n              <motion.div\n                key={feature.text}\n                className=\"flex items-center space-x-2 bg-dark-green-deep/30 backdrop-blur-sm border border-dark-green-muted rounded-full px-4 py-2\"\n                whileHover={{ scale: 1.05, borderColor: \"#00FF9E\" }}\n                transition={{ duration: 0.2 }}\n              >\n                <feature.icon className=\"w-4 h-4 text-primary-500\" />\n                <span className=\"text-sm text-text-secondary\">{feature.text}</span>\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div\n            variants={staggerItem}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n          >\n            <Button variant=\"magnetic\" size=\"xl\" className=\"group\">\n              Get Started\n              <ArrowRight className=\"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\" />\n            </Button>\n            <Button variant=\"outline\" size=\"xl\">\n              View Our Work\n            </Button>\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div\n            variants={staggerItem}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mt-20 pt-20 border-t border-dark-green-muted\"\n          >\n            {[\n              { number: \"50+\", label: \"Projects Delivered\" },\n              { number: \"25+\", label: \"Happy Clients\" },\n              { number: \"100%\", label: \"Success Rate\" },\n            ].map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                className=\"text-center\"\n                whileHover={{ scale: 1.05 }}\n              >\n                <div className=\"text-3xl md:text-4xl font-bold font-orbitron text-primary-500 mb-2\">\n                  {stat.number}\n                </div>\n                <div className=\"text-text-secondary\">{stat.label}</div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Floating Elements */}\n      <motion.div\n        className=\"absolute top-20 left-10 w-4 h-4 bg-primary-500 rounded-full opacity-60\"\n        animate={{\n          y: [0, -20, 0],\n          opacity: [0.6, 1, 0.6],\n        }}\n        transition={{\n          duration: 3,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n      />\n      <motion.div\n        className=\"absolute top-40 right-20 w-2 h-2 bg-primary-500 rounded-full opacity-40\"\n        animate={{\n          y: [0, -30, 0],\n          opacity: [0.4, 0.8, 0.4],\n        }}\n        transition={{\n          duration: 4,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 1,\n        }}\n      />\n      <motion.div\n        className=\"absolute bottom-40 left-20 w-3 h-3 bg-primary-500 rounded-full opacity-50\"\n        animate={{\n          y: [0, -25, 0],\n          opacity: [0.5, 0.9, 0.5],\n        }}\n        transition={{\n          duration: 3.5,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 2,\n        }}\n      />\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC,yJAAA,CAAA,iBAAc;gBAAC,OAAO;gBAAG,WAAU;;;;;;0BAGpC,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,sHAAA,CAAA,mBAAgB;oBAC1B,SAAQ;oBACR,SAAQ;;sCAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU,sHAAA,CAAA,cAAW;4BACrB,WAAU;;8CAEV,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAMzD,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU,sHAAA,CAAA,cAAW;4BACrB,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;8CAAY;;;;;;8CAC5B,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;;;;;;;sCAIrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU,sHAAA,CAAA,cAAW;4BACrB,WAAU;;gCACX;gCAC8C;8CAC7C,6LAAC;oCAAK,WAAU;8CAAiC;;;;;;gCAAwB;;;;;;;sCAI3E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU,sHAAA,CAAA,cAAW;4BACrB,WAAU;sCAET;gCACC;oCAAE,MAAM,qMAAA,CAAA,OAAI;oCAAE,MAAM;gCAAmB;gCACvC;oCAAE,MAAM,mMAAA,CAAA,MAAG;oCAAE,MAAM;gCAAkB;gCACrC;oCAAE,MAAM,yMAAA,CAAA,SAAM;oCAAE,MAAM;gCAAmB;6BAC1C,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,YAAY;wCAAE,OAAO;wCAAM,aAAa;oCAAU;oCAClD,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;sDACxB,6LAAC;4CAAK,WAAU;sDAA+B,QAAQ,IAAI;;;;;;;mCANtD,QAAQ,IAAI;;;;;;;;;;sCAYvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU,sHAAA,CAAA,cAAW;4BACrB,WAAU;;8CAEV,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAW,MAAK;oCAAK,WAAU;;wCAAQ;sDAErD,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;sCAMtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU,sHAAA,CAAA,cAAW;4BACrB,WAAU;sCAET;gCACC;oCAAE,QAAQ;oCAAO,OAAO;gCAAqB;gCAC7C;oCAAE,QAAQ;oCAAO,OAAO;gCAAgB;gCACxC;oCAAE,QAAQ;oCAAQ,OAAO;gCAAe;6BACzC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;;sDAE1B,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;sDAEd,6LAAC;4CAAI,WAAU;sDAAuB,KAAK,KAAK;;;;;;;mCAP3C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0BAezB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACT;;;;;;0BAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACT;;;;;;;;;;;;AAIR;KArJgB", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { motion } from \"framer-motion\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    variant?: \"default\" | \"glass\" | \"project\" | \"team\" | \"blob\"\n    hover3d?: boolean\n  }\n>(({ className, variant = \"default\", hover3d = false, children, ...props }, ref) => {\n  const baseClasses = \"rounded-lg border text-card-foreground shadow-sm\"\n  \n  const variantClasses = {\n    default: \"bg-dark-green-deep border-dark-green-muted\",\n    glass: \"glass border-white/10\",\n    project: \"bg-dark-green-deep border-dark-green-muted hover:border-primary-500 transition-all duration-300 group overflow-hidden\",\n    team: \"bg-dark-green-deep border-dark-green-muted hover:glow-accent-sm transition-all duration-300\",\n    blob: \"bg-gradient-to-br from-dark-green-deep to-dark-green-muted border-primary-500/20 relative overflow-hidden\"\n  }\n\n  if (hover3d) {\n    return (\n      <motion.div\n        ref={ref}\n        className={cn(baseClasses, variantClasses[variant], className)}\n        whileHover={{ \n          rotateX: 5, \n          rotateY: 5, \n          scale: 1.02,\n          transition: { duration: 0.3 }\n        }}\n        style={{ \n          transformStyle: \"preserve-3d\",\n          perspective: \"1000px\"\n        }}\n        {...props}\n      >\n        {variant === \"blob\" && (\n          <div className=\"absolute inset-0 opacity-20\">\n            <div className=\"blob absolute top-4 right-4 w-20 h-20 bg-primary-500/30\" />\n            <div className=\"blob absolute bottom-4 left-4 w-16 h-16 bg-primary-500/20\" style={{ animationDelay: \"2s\" }} />\n          </div>\n        )}\n        {children}\n      </motion.div>\n    )\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={cn(baseClasses, variantClasses[variant], className)}\n      {...props}\n    >\n      {variant === \"blob\" && (\n        <div className=\"absolute inset-0 opacity-20\">\n          <div className=\"blob absolute top-4 right-4 w-20 h-20 bg-primary-500/30\" />\n          <div className=\"blob absolute bottom-4 left-4 w-16 h-16 bg-primary-500/20\" style={{ animationDelay: \"2s\" }} />\n        </div>\n      )}\n      {children}\n    </div>\n  )\n})\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement> & {\n    glow?: boolean\n  }\n>(({ className, glow = false, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight font-orbitron\",\n      glow && \"text-glow\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-text-secondary\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAM1B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC1E,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;QACN,MAAM;IACR;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,cAAc,CAAC,QAAQ,EAAE;YACpD,YAAY;gBACV,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,YAAY;oBAAE,UAAU;gBAAI;YAC9B;YACA,OAAO;gBACL,gBAAgB;gBAChB,aAAa;YACf;YACC,GAAG,KAAK;;gBAER,YAAY,wBACX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;4BAA4D,OAAO;gCAAE,gBAAgB;4BAAK;;;;;;;;;;;;gBAG5G;;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,cAAc,CAAC,QAAQ,EAAE;QACnD,GAAG,KAAK;;YAER,YAAY,wBACX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAA4D,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;YAG5G;;;;;;;AAGP;;AACA,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAK/B,CAAC,EAAE,SAAS,EAAE,OAAO,KAAK,EAAE,GAAG,OAAO,EAAE,oBACxC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA,QAAQ,aACR;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/sections/services-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { Code, Smartphone, Gamepad2, Palette, Shield, Zap } from \"lucide-react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { staggerContainer, staggerItem } from \"@/lib/utils\"\n\nconst services = [\n  {\n    icon: Code,\n    title: \"DApp Development\",\n    description: \"Full-stack decentralized applications built with cutting-edge Web3 technologies.\",\n    features: [\"React/Next.js Frontend\", \"Smart Contract Integration\", \"Web3 Wallet Connection\", \"IPFS Storage\"],\n    color: \"from-primary-500 to-primary-400\",\n  },\n  {\n    icon: Shield,\n    title: \"Smart Contracts\",\n    description: \"Secure, audited smart contracts for Ethereum, Polygon, and other EVM chains.\",\n    features: [\"Solidity Development\", \"Security Audits\", \"Gas Optimization\", \"Multi-chain Support\"],\n    color: \"from-blue-500 to-blue-400\",\n  },\n  {\n    icon: Smartphone,\n    title: \"Telegram Bo<PERSON>\",\n    description: \"Intelligent bots for community management, trading, and Web3 interactions.\",\n    features: [\"Trading Bots\", \"Community Management\", \"NFT Alerts\", \"DeFi Integration\"],\n    color: \"from-purple-500 to-purple-400\",\n  },\n  {\n    icon: Gamepad2,\n    title: \"Game Development\",\n    description: \"Blockchain games with NFT integration and play-to-earn mechanics.\",\n    features: [\"Unity Integration\", \"NFT Marketplace\", \"Token Economics\", \"Multi-platform\"],\n    color: \"from-orange-500 to-orange-400\",\n  },\n  {\n    icon: Palette,\n    title: \"UI/UX Design\",\n    description: \"Modern, intuitive interfaces that make Web3 accessible to everyone.\",\n    features: [\"User Research\", \"Prototyping\", \"Design Systems\", \"Accessibility\"],\n    color: \"from-pink-500 to-pink-400\",\n  },\n  {\n    icon: Zap,\n    title: \"Web3 Infrastructure\",\n    description: \"Complete infrastructure setup for your Web3 project deployment.\",\n    features: [\"Node Setup\", \"API Development\", \"Database Design\", \"Cloud Deployment\"],\n    color: \"from-green-500 to-green-400\",\n  },\n]\n\nexport function ServicesSection() {\n  return (\n    <section className=\"py-20 relative\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"text-3xl md:text-5xl font-bold font-orbitron mb-4\">\n            Our <span className=\"text-primary-500\">Services</span>\n          </h2>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto\">\n            From concept to deployment, we provide end-to-end Web3 development services\n            that bring your blockchain vision to life.\n          </p>\n        </motion.div>\n\n        {/* Services Grid */}\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\"\n          variants={staggerContainer}\n          initial=\"initial\"\n          whileInView=\"animate\"\n          viewport={{ once: true }}\n        >\n          {services.map((service, index) => (\n            <motion.div key={service.title} variants={staggerItem}>\n              <Card \n                variant=\"project\" \n                hover3d \n                className=\"h-full group cursor-pointer\"\n              >\n                <CardHeader>\n                  <div className=\"flex items-center space-x-3 mb-4\">\n                    <div className={`p-3 rounded-lg bg-gradient-to-r ${service.color} group-hover:scale-110 transition-transform duration-300`}>\n                      <service.icon className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <CardTitle className=\"text-xl group-hover:text-primary-500 transition-colors duration-300\">\n                      {service.title}\n                    </CardTitle>\n                  </div>\n                  <CardDescription className=\"text-base\">\n                    {service.description}\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <ul className=\"space-y-2 mb-6\">\n                    {service.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-center text-sm text-text-secondary\">\n                        <div className=\"w-1.5 h-1.5 bg-primary-500 rounded-full mr-3\" />\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                  <Button \n                    variant=\"ghost\" \n                    className=\"w-full group-hover:bg-primary-500 group-hover:text-black transition-all duration-300\"\n                  >\n                    Learn More\n                  </Button>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div\n          className=\"text-center mt-16\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"bg-dark-green-deep/50 backdrop-blur-sm border border-primary-500/20 rounded-2xl p-8 max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl md:text-3xl font-bold font-orbitron mb-4\">\n              Ready to build the future?\n            </h3>\n            <p className=\"text-text-secondary mb-6 max-w-2xl mx-auto\">\n              Let's discuss your Web3 project and create something extraordinary together.\n              Our team is ready to turn your vision into reality.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button variant=\"magnetic\" size=\"lg\">\n                Start Your Project\n              </Button>\n              <Button variant=\"outline\" size=\"lg\">\n                Schedule Consultation\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,WAAW;IACf;QACE,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAA0B;YAA8B;YAA0B;SAAe;QAC5G,OAAO;IACT;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAwB;YAAmB;YAAoB;SAAsB;QAChG,OAAO;IACT;IACA;QACE,MAAM,iNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAgB;YAAwB;YAAc;SAAmB;QACpF,OAAO;IACT;IACA;QACE,MAAM,iNAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAqB;YAAmB;YAAmB;SAAiB;QACvF,OAAO;IACT;IACA;QACE,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAiB;YAAe;YAAkB;SAAgB;QAC7E,OAAO;IACT;IACA;QACE,MAAM,mMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAc;YAAmB;YAAmB;SAAmB;QAClF,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,6LAAC;4BAAG,WAAU;;gCAAoD;8CAC5D,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;;;;;;;sCAEzC,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAO/D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,sHAAA,CAAA,mBAAgB;oBAC1B,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;8BAEtB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAqB,UAAU,sHAAA,CAAA,cAAW;sCACnD,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCACH,SAAQ;gCACR,OAAO;gCACP,WAAU;;kDAEV,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,gCAAgC,EAAE,QAAQ,KAAK,CAAC,wDAAwD,CAAC;kEACxH,cAAA,6LAAC,QAAQ,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAE1B,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,QAAQ,KAAK;;;;;;;;;;;;0DAGlB,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,QAAQ,WAAW;;;;;;;;;;;;kDAGxB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEAAI,WAAU;;;;;;4DACd;;uDAFM;;;;;;;;;;0DAMb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;;;;;;;;2BA/BU,QAAQ,KAAK;;;;;;;;;;8BAyClC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAI1D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAW,MAAK;kDAAK;;;;;;kDAGrC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD;KAnGgB", "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/sections/projects-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { <PERSON>Link, Gith<PERSON>, ArrowRight } from \"lucide-react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { staggerContainer, staggerItem } from \"@/lib/utils\"\n\nconst projects = [\n  {\n    title: \"DeFi Trading Platform\",\n    description: \"A comprehensive DeFi platform with yield farming, staking, and automated trading strategies.\",\n    image: \"/api/placeholder/400/250\",\n    tags: [\"DeFi\", \"React\", \"Solidity\", \"Web3\"],\n    category: \"DApp\",\n    status: \"Live\",\n    links: {\n      live: \"https://example.com\",\n      github: \"https://github.com/example\",\n    }\n  },\n  {\n    title: \"NFT Marketplace\",\n    description: \"Multi-chain NFT marketplace with advanced filtering, bidding, and royalty management.\",\n    image: \"/api/placeholder/400/250\",\n    tags: [\"NFT\", \"Next.js\", \"IPFS\", \"Polygon\"],\n    category: \"Marketplace\",\n    status: \"Live\",\n    links: {\n      live: \"https://example.com\",\n      github: \"https://github.com/example\",\n    }\n  },\n  {\n    title: \"GameFi RPG\",\n    description: \"Play-to-earn RPG game with NFT characters, land ownership, and token rewards.\",\n    image: \"/api/placeholder/400/250\",\n    tags: [\"GameFi\", \"Unity\", \"P2E\", \"NFT\"],\n    category: \"Game\",\n    status: \"Beta\",\n    links: {\n      live: \"https://example.com\",\n    }\n  },\n  {\n    title: \"DAO Governance Platform\",\n    description: \"Decentralized governance platform with proposal creation, voting, and treasury management.\",\n    image: \"/api/placeholder/400/250\",\n    tags: [\"DAO\", \"Governance\", \"React\", \"Ethereum\"],\n    category: \"DApp\",\n    status: \"Live\",\n    links: {\n      live: \"https://example.com\",\n      github: \"https://github.com/example\",\n    }\n  },\n  {\n    title: \"Crypto Trading Bot\",\n    description: \"Intelligent Telegram bot for automated crypto trading with advanced analytics.\",\n    image: \"/api/placeholder/400/250\",\n    tags: [\"Bot\", \"Trading\", \"AI\", \"Telegram\"],\n    category: \"Bot\",\n    status: \"Live\",\n    links: {\n      live: \"https://t.me/example\",\n    }\n  },\n  {\n    title: \"Cross-Chain Bridge\",\n    description: \"Secure bridge for transferring assets between Ethereum, Polygon, and BSC networks.\",\n    image: \"/api/placeholder/400/250\",\n    tags: [\"Bridge\", \"Multi-chain\", \"Security\", \"DeFi\"],\n    category: \"Infrastructure\",\n    status: \"Live\",\n    links: {\n      live: \"https://example.com\",\n      github: \"https://github.com/example\",\n    }\n  },\n]\n\nexport function ProjectsSection() {\n  return (\n    <section className=\"py-20 relative\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"text-3xl md:text-5xl font-bold font-orbitron mb-4\">\n            Featured <span className=\"text-primary-500\">Projects</span>\n          </h2>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto\">\n            Explore our portfolio of successful Web3 projects that showcase\n            our expertise and innovation in blockchain development.\n          </p>\n        </motion.div>\n\n        {/* Projects Grid */}\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\"\n          variants={staggerContainer}\n          initial=\"initial\"\n          whileInView=\"animate\"\n          viewport={{ once: true }}\n        >\n          {projects.map((project, index) => (\n            <motion.div key={project.title} variants={staggerItem}>\n              <Card variant=\"project\" hover3d className=\"h-full group overflow-hidden\">\n                {/* Project Image */}\n                <div className=\"relative h-48 bg-gradient-to-br from-dark-green-deep to-dark-green-muted overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-grid-pattern opacity-20\" />\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"text-6xl font-bold font-orbitron text-primary-500/20\">\n                      {project.category}\n                    </div>\n                  </div>\n                  \n                  {/* Status Badge */}\n                  <div className=\"absolute top-4 right-4\">\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                      project.status === 'Live' \n                        ? 'bg-green-500/20 text-green-400 border border-green-500/30' \n                        : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'\n                    }`}>\n                      {project.status}\n                    </span>\n                  </div>\n\n                  {/* Hover Overlay */}\n                  <div className=\"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-4\">\n                    {project.links.live && (\n                      <Button variant=\"ghost\" size=\"icon\" className=\"text-white hover:text-primary-500\">\n                        <ExternalLink className=\"w-5 h-5\" />\n                      </Button>\n                    )}\n                    {project.links.github && (\n                      <Button variant=\"ghost\" size=\"icon\" className=\"text-white hover:text-primary-500\">\n                        <Github className=\"w-5 h-5\" />\n                      </Button>\n                    )}\n                  </div>\n                </div>\n\n                <CardHeader>\n                  <CardTitle className=\"group-hover:text-primary-500 transition-colors duration-300\">\n                    {project.title}\n                  </CardTitle>\n                  <CardDescription>\n                    {project.description}\n                  </CardDescription>\n                </CardHeader>\n\n                <CardContent>\n                  {/* Tags */}\n                  <div className=\"flex flex-wrap gap-2 mb-4\">\n                    {project.tags.map((tag) => (\n                      <span\n                        key={tag}\n                        className=\"px-2 py-1 bg-dark-green-muted text-primary-500 rounded-md text-xs font-medium\"\n                      >\n                        {tag}\n                      </span>\n                    ))}\n                  </div>\n\n                  {/* Links */}\n                  <div className=\"flex space-x-2\">\n                    {project.links.live && (\n                      <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                        View Live\n                        <ExternalLink className=\"w-3 h-3 ml-1\" />\n                      </Button>\n                    )}\n                    {project.links.github && (\n                      <Button variant=\"ghost\" size=\"sm\">\n                        <Github className=\"w-4 h-4\" />\n                      </Button>\n                    )}\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* View All Projects CTA */}\n        <motion.div\n          className=\"text-center\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          viewport={{ once: true }}\n        >\n          <Button variant=\"magnetic\" size=\"lg\" className=\"group\">\n            View All Projects\n            <ArrowRight className=\"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\" />\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,WAAW;IACf;QACE,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAQ;YAAS;YAAY;SAAO;QAC3C,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,QAAQ;QACV;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAO;YAAW;YAAQ;SAAU;QAC3C,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,QAAQ;QACV;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAU;YAAS;YAAO;SAAM;QACvC,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAO;YAAc;YAAS;SAAW;QAChD,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,QAAQ;QACV;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAO;YAAW;YAAM;SAAW;QAC1C,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;YAAC;YAAU;YAAe;YAAY;SAAO;QACnD,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,QAAQ;QACV;IACF;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,6LAAC;4BAAG,WAAU;;gCAAoD;8CACvD,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;;;;;;;sCAE9C,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAO/D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,sHAAA,CAAA,mBAAgB;oBAC1B,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;8BAEtB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAqB,UAAU,sHAAA,CAAA,cAAW;sCACnD,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAU,OAAO;gCAAC,WAAU;;kDAExC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,QAAQ;;;;;;;;;;;0DAKrB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAW,CAAC,2CAA2C,EAC3D,QAAQ,MAAM,KAAK,SACf,8DACA,gEACJ;8DACC,QAAQ,MAAM;;;;;;;;;;;0DAKnB,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,KAAK,CAAC,IAAI,kBACjB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAO,WAAU;kEAC5C,cAAA,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;oDAG3B,QAAQ,KAAK,CAAC,MAAM,kBACnB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAO,WAAU;kEAC5C,cAAA,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAM1B,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,QAAQ,KAAK;;;;;;0DAEhB,6LAAC,mIAAA,CAAA,kBAAe;0DACb,QAAQ,WAAW;;;;;;;;;;;;kDAIxB,6LAAC,mIAAA,CAAA,cAAW;;0DAEV,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,oBACjB,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;0DASX,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,KAAK,CAAC,IAAI,kBACjB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,WAAU;;4DAAS;0EAErD,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;oDAG3B,QAAQ,KAAK,CAAC,MAAM,kBACnB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BArEb,QAAQ,KAAK;;;;;;;;;;8BAgFlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAW,MAAK;wBAAK,WAAU;;4BAAQ;0CAErD,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KA7HgB", "debugId": null}}, {"offset": {"line": 1383, "column": 0}, "map": {"version": 3, "file": "arrow-right.js", "sources": ["file:///Users/<USER>/Desktop/clones/labs/node_modules/lucide-react/src/icons/arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n];\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('arrow-right', __iconNode);\n\nexport default ArrowRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1429, "column": 0}, "map": {"version": 3, "file": "code.js", "sources": ["file:///Users/<USER>/Desktop/clones/labs/node_modules/lucide-react/src/icons/code.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm16 18 6-6-6-6', key: 'eg8j8' }],\n  ['path', { d: 'm8 6-6 6 6 6', key: 'ppft3o' }],\n];\n\n/**\n * @component @name Code\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgMTggNi02LTYtNiIgLz4KICA8cGF0aCBkPSJtOCA2LTYgNiA2IDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/code\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Code = createLucideIcon('code', __iconNode);\n\nexport default Code;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA;IAC9C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC/C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1475, "column": 0}, "map": {"version": 3, "file": "rocket.js", "sources": ["file:///Users/<USER>/Desktop/clones/labs/node_modules/lucide-react/src/icons/rocket.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z',\n      key: 'm3kijz',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'm12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z',\n      key: '1fmvmk',\n    },\n  ],\n  ['path', { d: 'M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0', key: '1f8sc4' }],\n  ['path', { d: 'M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5', key: 'qeys4' }],\n];\n\n/**\n * @component @name Rocket\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNC41IDE2LjVjLTEuNSAxLjI2LTIgNS0yIDVzMy43NC0uNSA1LTJjLjcxLS44NC43LTIuMTMtLjA5LTIuOTFhMi4xOCAyLjE4IDAgMCAwLTIuOTEtLjA5eiIgLz4KICA8cGF0aCBkPSJtMTIgMTUtMy0zYTIyIDIyIDAgMCAxIDItMy45NUExMi44OCAxMi44OCAwIDAgMSAyMiAyYzAgMi43Mi0uNzggNy41LTYgMTFhMjIuMzUgMjIuMzUgMCAwIDEtNCAyeiIgLz4KICA8cGF0aCBkPSJNOSAxMkg0cy41NS0zLjAzIDItNGMxLjYyLTEuMDggNSAwIDUgMCIgLz4KICA8cGF0aCBkPSJNMTIgMTV2NXMzLjAzLS41NSA0LTJjMS4wOC0xLjYyIDAtNSAwLTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/rocket\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Rocket = createLucideIcon('rocket', __iconNode);\n\nexport default Rocket;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA;CACzE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1535, "column": 0}, "map": {"version": 3, "file": "smartphone.js", "sources": ["file:///Users/<USER>/Desktop/clones/labs/node_modules/lucide-react/src/icons/smartphone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '14', height: '20', x: '5', y: '2', rx: '2', ry: '2', key: '1yt0o3' }],\n  ['path', { d: 'M12 18h.01', key: 'mhygvu' }],\n];\n\n/**\n * @component @name Smartphone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iMjAiIHg9IjUiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNMTIgMThoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/smartphone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Smartphone = createLucideIcon('smartphone', __iconNode);\n\nexport default Smartphone;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,EAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAG;YAAK,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1586, "column": 0}, "map": {"version": 3, "file": "gamepad-2.js", "sources": ["file:///Users/<USER>/Desktop/clones/labs/node_modules/lucide-react/src/icons/gamepad-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '6', x2: '10', y1: '11', y2: '11', key: '1gktln' }],\n  ['line', { x1: '8', x2: '8', y1: '9', y2: '13', key: 'qnk9ow' }],\n  ['line', { x1: '15', x2: '15.01', y1: '12', y2: '12', key: 'krot7o' }],\n  ['line', { x1: '18', x2: '18.01', y1: '10', y2: '10', key: '1lcuu1' }],\n  [\n    'path',\n    {\n      d: 'M17.32 5H6.68a4 4 0 0 0-3.978 3.59c-.006.052-.01.101-.017.152C2.604 9.416 2 14.456 2 16a3 3 0 0 0 3 3c1 0 1.5-.5 2-1l1.414-1.414A2 2 0 0 1 9.828 16h4.344a2 2 0 0 1 1.414.586L17 18c.5.5 1 1 2 1a3 3 0 0 0 3-3c0-1.545-.604-6.584-.685-7.258-.007-.05-.011-.1-.017-.151A4 4 0 0 0 17.32 5z',\n      key: 'mfqc10',\n    },\n  ],\n];\n\n/**\n * @component @name Gamepad2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iNiIgeDI9IjEwIiB5MT0iMTEiIHkyPSIxMSIgLz4KICA8bGluZSB4MT0iOCIgeDI9IjgiIHkxPSI5IiB5Mj0iMTMiIC8+CiAgPGxpbmUgeDE9IjE1IiB4Mj0iMTUuMDEiIHkxPSIxMiIgeTI9IjEyIiAvPgogIDxsaW5lIHgxPSIxOCIgeDI9IjE4LjAxIiB5MT0iMTAiIHkyPSIxMCIgLz4KICA8cGF0aCBkPSJNMTcuMzIgNUg2LjY4YTQgNCAwIDAgMC0zLjk3OCAzLjU5Yy0uMDA2LjA1Mi0uMDEuMTAxLS4wMTcuMTUyQzIuNjA0IDkuNDE2IDIgMTQuNDU2IDIgMTZhMyAzIDAgMCAwIDMgM2MxIDAgMS41LS41IDItMWwxLjQxNC0xLjQxNEEyIDIgMCAwIDEgOS44MjggMTZoNC4zNDRhMiAyIDAgMCAxIDEuNDE0LjU4NkwxNyAxOGMuNS41IDEgMSAyIDFhMyAzIDAgMCAwIDMtM2MwLTEuNTQ1LS42MDQtNi41ODQtLjY4NS03LjI1OC0uMDA3LS4wNS0uMDExLS4xLS4wMTctLjE1MUE0IDQgMCAwIDAgMTcuMzIgNXoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/gamepad-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Gamepad2 = createLucideIcon('gamepad-2', __iconNode);\n\nexport default Gamepad2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACrE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACrE;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1665, "column": 0}, "map": {"version": 3, "file": "palette.js", "sources": ["file:///Users/<USER>/Desktop/clones/labs/node_modules/lucide-react/src/icons/palette.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z',\n      key: 'e79jfc',\n    },\n  ],\n  ['circle', { cx: '13.5', cy: '6.5', r: '.5', fill: 'currentColor', key: '1okk4w' }],\n  ['circle', { cx: '17.5', cy: '10.5', r: '.5', fill: 'currentColor', key: 'f64h9f' }],\n  ['circle', { cx: '6.5', cy: '12.5', r: '.5', fill: 'currentColor', key: 'qy21gx' }],\n  ['circle', { cx: '8.5', cy: '7.5', r: '.5', fill: 'currentColor', key: 'fotxhn' }],\n];\n\n/**\n * @component @name Palette\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJhMSAxIDAgMCAxIDAtMjAgMTAgOSAwIDAgMSAxMCA5IDUgNSAwIDAgMS01IDVoLTIuMjVhMS43NSAxLjc1IDAgMCAwLTEuNCAyLjhsLjMuNGExLjc1IDEuNzUgMCAwIDEtMS40IDIuOHoiIC8+CiAgPGNpcmNsZSBjeD0iMTMuNSIgY3k9IjYuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KICA8Y2lyY2xlIGN4PSIxNy41IiBjeT0iMTAuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KICA8Y2lyY2xlIGN4PSI2LjUiIGN5PSIxMi41IiByPSIuNSIgZmlsbD0iY3VycmVudENvbG9yIiAvPgogIDxjaXJjbGUgY3g9IjguNSIgY3k9IjcuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/palette\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Palette = createLucideIcon('palette', __iconNode);\n\nexport default Palette;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAE;YAAA,CAAA,CAAA,EAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAE;YAAA,CAAA,CAAA,EAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAE;YAAA,CAAA,CAAA,EAAI,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAE;YAAA,CAAA,CAAA,EAAI,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1744, "column": 0}, "map": {"version": 3, "file": "shield.js", "sources": ["file:///Users/<USER>/Desktop/clones/labs/node_modules/lucide-react/src/icons/shield.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z',\n      key: 'oel41y',\n    },\n  ],\n];\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTNjMCA1LTMuNSA3LjUtNy42NiA4Ljk1YTEgMSAwIDAgMS0uNjctLjAxQzcuNSAyMC41IDQgMTggNCAxM1Y2YTEgMSAwIDAgMSAxLTFjMiAwIDQuNS0xLjIgNi4yNC0yLjcyYTEuMTcgMS4xNyAwIDAgMSAxLjUyIDBDMTQuNTEgMy44MSAxNyA1IDE5IDVhMSAxIDAgMCAxIDEgMXoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('shield', __iconNode);\n\nexport default Shield;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport const fadeInUp = {\n  initial: { opacity: 0, y: 60 },\n  animate: { opacity: 1, y: 0 },\n  transition: { duration: 0.6, ease: \"easeOut\" }\n}\n\nexport const fadeInLeft = {\n  initial: { opacity: 0, x: -60 },\n  animate: { opacity: 1, x: 0 },\n  transition: { duration: 0.6, ease: \"easeOut\" }\n}\n\nexport const fadeInRight = {\n  initial: { opacity: 0, x: 60 },\n  animate: { opacity: 1, x: 0 },\n  transition: { duration: 0.6, ease: \"easeOut\" }\n}\n\nexport const scaleIn = {\n  initial: { opacity: 0, scale: 0.8 },\n  animate: { opacity: 1, scale: 1 },\n  transition: { duration: 0.5, ease: \"easeOut\" }\n}\n\nexport const staggerContainer = {\n  animate: {\n    transition: {\n      staggerChildren: 0.1,\n      delayChildren: 0.3,\n    },\n  },\n}\n\nexport const staggerItem = {\n  initial: { opacity: 0, y: 20 },\n  animate: { opacity: 1, y: 0 },\n  transition: { duration: 0.5, ease: \"easeOut\" }\n}\n\n// Utility for creating blob paths\nexport const generateBlobPath = () => {\n  const points = 6;\n  const radius = 100;\n  const centerX = 150;\n  const centerY = 150;\n  \n  let path = `M ${centerX + radius} ${centerY}`;\n  \n  for (let i = 1; i <= points; i++) {\n    const angle = (i * 2 * Math.PI) / points;\n    const variation = 0.3 + Math.random() * 0.4; // Random variation\n    const x = centerX + Math.cos(angle) * radius * variation;\n    const y = centerY + Math.sin(angle) * radius * variation;\n    \n    if (i === 1) {\n      path += ` Q ${x} ${y}`;\n    } else {\n      path += ` T ${x} ${y}`;\n    }\n  }\n  \n  path += ' Z';\n  return path;\n}\n\n// Debounce utility\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Throttle utility\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,WAAW;IACtB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;QAAK,MAAM;IAAU;AAC/C;AAEO,MAAM,aAAa;IACxB,SAAS;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;IAC9B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;QAAK,MAAM;IAAU;AAC/C;AAEO,MAAM,cAAc;IACzB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;QAAK,MAAM;IAAU;AAC/C;AAEO,MAAM,UAAU;IACrB,SAAS;QAAE,SAAS;QAAG,OAAO;IAAI;IAClC,SAAS;QAAE,SAAS;QAAG,OAAO;IAAE;IAChC,YAAY;QAAE,UAAU;QAAK,MAAM;IAAU;AAC/C;AAEO,MAAM,mBAAmB;IAC9B,SAAS;QACP,YAAY;YACV,iBAAiB;YACjB,eAAe;QACjB;IACF;AACF;AAEO,MAAM,cAAc;IACzB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;QAAK,MAAM;IAAU;AAC/C;AAGO,MAAM,mBAAmB;IAC9B,MAAM,SAAS;IACf,MAAM,SAAS;IACf,MAAM,UAAU;IAChB,MAAM,UAAU;IAEhB,IAAI,OAAO,CAAC,EAAE,EAAE,UAAU,OAAO,CAAC,EAAE,SAAS;IAE7C,IAAK,IAAI,IAAI,GAAG,KAAK,QAAQ,IAAK;QAChC,MAAM,QAAQ,AAAC,IAAI,IAAI,KAAK,EAAE,GAAI;QAClC,MAAM,YAAY,MAAM,KAAK,MAAM,KAAK,KAAK,mBAAmB;QAChE,MAAM,IAAI,UAAU,KAAK,GAAG,CAAC,SAAS,SAAS;QAC/C,MAAM,IAAI,UAAU,KAAK,GAAG,CAAC,SAAS,SAAS;QAE/C,IAAI,MAAM,GAAG;YACX,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG;QACxB,OAAO;YACL,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG;QACxB;IACF;IAEA,QAAQ;IACR,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { motion } from \"framer-motion\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary-500 text-black hover:bg-primary-400 glow-accent-sm hover:glow-accent font-semibold\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"border border-primary-500 bg-transparent text-primary-500 hover:bg-primary-500 hover:text-black glow-accent-sm hover:glow-accent\",\n        secondary: \"bg-dark-green-muted text-white hover:bg-dark-green-dark\",\n        ghost: \"text-primary-500 hover:bg-dark-green-deep hover:text-primary-400 transition-all duration-300\",\n        link: \"text-primary-500 underline-offset-4 hover:underline\",\n        magnetic: \"bg-primary-500 text-black hover:bg-primary-400 magnetic ripple font-semibold glow-accent-sm hover:glow-accent\",\n        flicker: \"bg-transparent border border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-black animate-flicker\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        xl: \"h-14 rounded-lg px-12 text-lg\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  withSpark?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, withSpark = false, children, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    \n    const buttonContent = (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      >\n        {children}\n        {withSpark && (\n          <span className=\"absolute inset-0 overflow-hidden rounded-md\">\n            <span className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-primary-500/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out\" />\n          </span>\n        )}\n      </Comp>\n    )\n\n    if (variant === \"magnetic\") {\n      return (\n        <motion.div\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n          className=\"relative group\"\n        >\n          {buttonContent}\n        </motion.div>\n      )\n    }\n\n    return buttonContent\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,UAAU;YACV,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACrF,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,MAAM,8BACJ,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;YAER;YACA,2BACC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAK,WAAU;;;;;;;;;;;;;;;;;IAMxB,IAAI,YAAY,YAAY;QAC1B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,YAAY;gBAAE,OAAO;YAAK;YAC1B,UAAU;gBAAE,OAAO;YAAK;YACxB,WAAU;sBAET;;;;;;IAGP;IAEA,OAAO;AACT;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/layout/navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport Link from \"next/link\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { Menu, X, Zap } from \"lucide-react\"\nimport { Button } from \"@/components/ui/button\"\nimport { cn } from \"@/lib/utils\"\n\nconst navItems = [\n  { name: \"Home\", href: \"/\" },\n  { name: \"Projects\", href: \"/projects\" },\n  { name: \"Services\", href: \"/services\" },\n  { name: \"Team\", href: \"/team\" },\n  { name: \"About\", href: \"/about\" },\n  { name: \"Blog\", href: \"/blog\" },\n  { name: \"Contact\", href: \"/contact\" },\n]\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [scrolled, setScrolled] = useState(false)\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50)\n    }\n\n    window.addEventListener(\"scroll\", handleScroll)\n    return () => window.removeEventListener(\"scroll\", handleScroll)\n  }, [])\n\n  return (\n    <motion.nav\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\",\n        scrolled ? \"glass backdrop-blur-md\" : \"bg-transparent\"\n      )}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            className=\"flex items-center space-x-2\"\n            whileHover={{ scale: 1.05 }}\n          >\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"relative\">\n                <Zap className=\"w-8 h-8 text-primary-500\" />\n                <motion.div\n                  className=\"absolute inset-0 w-8 h-8 text-primary-500\"\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 8, repeat: Infinity, ease: \"linear\" }}\n                >\n                  <Zap className=\"w-8 h-8\" />\n                </motion.div>\n              </div>\n              <span className=\"text-xl font-bold font-orbitron text-glow\">\n                BuildLabz\n              </span>\n            </Link>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item, index) => (\n                <motion.div\n                  key={item.name}\n                  initial={{ opacity: 0, y: -20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  <Link\n                    href={item.href}\n                    className=\"text-text-secondary hover:text-primary-500 px-3 py-2 rounded-md text-sm font-medium transition-all duration-300 relative group\"\n                  >\n                    {item.name}\n                    <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-primary-500 group-hover:w-full transition-all duration-300\" />\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:block\">\n            <Button variant=\"magnetic\" size=\"sm\">\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-text-secondary hover:text-primary-500\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            className=\"md:hidden glass backdrop-blur-md border-t border-white/10\"\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n              {navItems.map((item, index) => (\n                <motion.div\n                  key={item.name}\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  <Link\n                    href={item.href}\n                    className=\"text-text-secondary hover:text-primary-500 block px-3 py-2 rounded-md text-base font-medium transition-all duration-300\"\n                    onClick={() => setIsOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                </motion.div>\n              ))}\n              <motion.div\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: navItems.length * 0.1 }}\n                className=\"pt-4\"\n              >\n                <Button variant=\"magnetic\" className=\"w-full\">\n                  Get Started\n                </Button>\n              </motion.div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,WAAW,2BAA2B;QAExC,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;sCAE1B,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,QAAQ;gDAAI;gDACvB,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAS;0DAE5D,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGnB,8OAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;;;;;;;sCAOhE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;kDAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;;gDAET,KAAK,IAAI;8DACV,8OAAC;oDAAK,WAAU;;;;;;;;;;;;uCAVb,KAAK,IAAI;;;;;;;;;;;;;;;sCAkBtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAW,MAAK;0CAAK;;;;;;;;;;;sCAMvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;8CAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,UAAU;kDAExB,KAAK,IAAI;;;;;;mCAVP,KAAK,IAAI;;;;;0CAclB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,SAAS,MAAM,GAAG;gCAAI;gCAC3C,WAAU;0CAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAW,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9D", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/layout/footer.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { motion } from \"framer-motion\"\nimport { Github, Twitter, Linkedin, Mail, Zap, ExternalLink } from \"lucide-react\"\nimport { Button } from \"@/components/ui/button\"\n\nconst footerLinks = {\n  services: [\n    { name: \"DApp Development\", href: \"/services#dapp\" },\n    { name: \"Smart Contracts\", href: \"/services#contracts\" },\n    { name: \"Telegram Bots\", href: \"/services#bots\" },\n    { name: \"Game Development\", href: \"/services#games\" },\n    { name: \"UI/UX Design\", href: \"/services#design\" },\n  ],\n  company: [\n    { name: \"About Us\", href: \"/about\" },\n    { name: \"Our Team\", href: \"/team\" },\n    { name: \"Careers\", href: \"/careers\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  resources: [\n    { name: \"Documentation\", href: \"/docs\" },\n    { name: \"Case Studies\", href: \"/projects\" },\n    { name: \"Open Source\", href: \"/open-source\" },\n    { name: \"Community\", href: \"/community\" },\n    { name: \"Support\", href: \"/support\" },\n  ],\n}\n\nconst socialLinks = [\n  { name: \"GitHub\", icon: Github, href: \"https://github.com/buildlabz\" },\n  { name: \"Twitter\", icon: Twitter, href: \"https://twitter.com/buildlabz\" },\n  { name: \"LinkedIn\", icon: Linkedin, href: \"https://linkedin.com/company/buildlabz\" },\n  { name: \"Email\", icon: Mail, href: \"mailto:<EMAIL>\" },\n]\n\nexport function Footer() {\n  return (\n    <footer className=\"relative bg-dark-green-deep border-t border-dark-green-muted\">\n      {/* Grid background */}\n      <div className=\"absolute inset-0 grid-bg opacity-20\" />\n      \n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <motion.div\n              className=\"flex items-center space-x-2 mb-4\"\n              whileHover={{ scale: 1.05 }}\n            >\n              <div className=\"relative\">\n                <Zap className=\"w-8 h-8 text-primary-500\" />\n                <motion.div\n                  className=\"absolute inset-0 w-8 h-8 text-primary-500\"\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 8, repeat: Infinity, ease: \"linear\" }}\n                >\n                  <Zap className=\"w-8 h-8\" />\n                </motion.div>\n              </div>\n              <span className=\"text-2xl font-bold font-orbitron text-glow\">\n                BuildLabz\n              </span>\n            </motion.div>\n            \n            <p className=\"text-text-secondary mb-6 max-w-md\">\n              We forge the future of Web3. One block at a time. From idea to reality in weeks, not months.\n            </p>\n            \n            <div className=\"flex space-x-4\">\n              {socialLinks.map((social) => (\n                <motion.a\n                  key={social.name}\n                  href={social.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-text-secondary hover:text-primary-500 transition-colors duration-300\"\n                  whileHover={{ scale: 1.2, rotate: 5 }}\n                  whileTap={{ scale: 0.9 }}\n                >\n                  <social.icon className=\"w-5 h-5\" />\n                  <span className=\"sr-only\">{social.name}</span>\n                </motion.a>\n              ))}\n            </div>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold font-orbitron text-primary-500 mb-4\">\n              Services\n            </h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.services.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-text-secondary hover:text-primary-500 transition-colors duration-300 text-sm\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"text-lg font-semibold font-orbitron text-primary-500 mb-4\">\n              Company\n            </h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-text-secondary hover:text-primary-500 transition-colors duration-300 text-sm\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Resources */}\n          <div>\n            <h3 className=\"text-lg font-semibold font-orbitron text-primary-500 mb-4\">\n              Resources\n            </h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.resources.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-text-secondary hover:text-primary-500 transition-colors duration-300 text-sm flex items-center\"\n                  >\n                    {link.name}\n                    <ExternalLink className=\"w-3 h-3 ml-1\" />\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <motion.div\n          className=\"mt-12 pt-8 border-t border-dark-green-muted\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <div className=\"mb-4 md:mb-0\">\n              <h3 className=\"text-lg font-semibold font-orbitron text-primary-500 mb-2\">\n                Stay Updated\n              </h3>\n              <p className=\"text-text-secondary text-sm\">\n                Get the latest updates on Web3 development and BuildLabz news.\n              </p>\n            </div>\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"px-4 py-2 bg-dark-green-muted border border-dark-green-dark rounded-md text-white placeholder-text-secondary focus:outline-none focus:border-primary-500 focus:glow-accent-sm\"\n              />\n              <Button variant=\"magnetic\" size=\"sm\">\n                Subscribe\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Bottom Bar */}\n        <div className=\"mt-8 pt-8 border-t border-dark-green-muted flex flex-col md:flex-row items-center justify-between\">\n          <p className=\"text-text-secondary text-sm\">\n            © 2024 BuildLabz. All rights reserved.\n          </p>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <Link href=\"/privacy\" className=\"text-text-secondary hover:text-primary-500 text-sm transition-colors duration-300\">\n              Privacy Policy\n            </Link>\n            <Link href=\"/terms\" className=\"text-text-secondary hover:text-primary-500 text-sm transition-colors duration-300\">\n              Terms of Service\n            </Link>\n            <Link href=\"/cookies\" className=\"text-text-secondary hover:text-primary-500 text-sm transition-colors duration-300\">\n              Cookie Policy\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,cAAc;IAClB,UAAU;QACR;YAAE,MAAM;YAAoB,MAAM;QAAiB;QACnD;YAAE,MAAM;YAAmB,MAAM;QAAsB;QACvD;YAAE,MAAM;YAAiB,MAAM;QAAiB;QAChD;YAAE,MAAM;YAAoB,MAAM;QAAkB;QACpD;YAAE,MAAM;YAAgB,MAAM;QAAmB;KAClD;IACD,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAS;QACnC;YAAE,MAAM;YAAY,MAAM;QAAQ;QAClC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,WAAW;QACT;YAAE,MAAM;YAAiB,MAAM;QAAQ;QACvC;YAAE,MAAM;YAAgB,MAAM;QAAY;QAC1C;YAAE,MAAM;YAAe,MAAM;QAAe;QAC5C;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAU,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;IAA+B;IACrE;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;IAAgC;IACxE;QAAE,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;IAAyC;IACnF;QAAE,MAAM;QAAS,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;IAA6B;CACjE;AAEM,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;;0DAE1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,QAAQ;wDAAI;wDACvB,YAAY;4DAAE,UAAU;4DAAG,QAAQ;4DAAU,MAAM;wDAAS;kEAE5D,cAAA,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGnB,8OAAC;gDAAK,WAAU;0DAA6C;;;;;;;;;;;;kDAK/D,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;kDAIjD,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDAEP,MAAM,OAAO,IAAI;gDACjB,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,YAAY;oDAAE,OAAO;oDAAK,QAAQ;gDAAE;gDACpC,UAAU;oDAAE,OAAO;gDAAI;;kEAEvB,8OAAC,OAAO,IAAI;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAK,WAAU;kEAAW,OAAO,IAAI;;;;;;;+CATjC,OAAO,IAAI;;;;;;;;;;;;;;;;0CAgBxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;kDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;;wDAET,KAAK,IAAI;sEACV,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;+CANnB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAe1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,8OAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;8CAI7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAW,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAQ3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAG3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoF;;;;;;kDAGpH,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAoF;;;;;;kDAGlH,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhI", "debugId": null}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/animations/blob-background.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { useEffect, useState } from \"react\"\n\ninterface BlobBackgroundProps {\n  className?: string\n  count?: number\n  animated?: boolean\n}\n\nexport function BlobBackground({ className = \"\", count = 3, animated = true }: BlobBackgroundProps) {\n  const [blobs, setBlobs] = useState<Array<{ id: number; x: number; y: number; size: number; delay: number }>>([])\n\n  useEffect(() => {\n    const generateBlobs = () => {\n      return Array.from({ length: count }, (_, i) => ({\n        id: i,\n        x: Math.random() * 100,\n        y: Math.random() * 100,\n        size: 200 + Math.random() * 300,\n        delay: Math.random() * 5,\n      }))\n    }\n\n    setBlobs(generateBlobs())\n  }, [count])\n\n  return (\n    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>\n      {blobs.map((blob) => (\n        <motion.div\n          key={blob.id}\n          className=\"absolute rounded-full opacity-10 bg-gradient-to-br from-primary-500 to-primary-300\"\n          style={{\n            left: `${blob.x}%`,\n            top: `${blob.y}%`,\n            width: blob.size,\n            height: blob.size,\n            transform: \"translate(-50%, -50%)\",\n          }}\n          animate={animated ? {\n            x: [0, 30, -30, 0],\n            y: [0, -30, 30, 0],\n            scale: [1, 1.1, 0.9, 1],\n            rotate: [0, 90, 180, 270, 360],\n          } : {}}\n          transition={{\n            duration: 20 + Math.random() * 10,\n            repeat: Infinity,\n            ease: \"linear\",\n            delay: blob.delay,\n          }}\n        />\n      ))}\n      \n      {/* Additional floating particles */}\n      {Array.from({ length: 20 }).map((_, i) => (\n        <motion.div\n          key={`particle-${i}`}\n          className=\"absolute w-1 h-1 bg-primary-500 rounded-full opacity-30\"\n          style={{\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`,\n          }}\n          animate={animated ? {\n            y: [0, -100, 0],\n            opacity: [0.3, 0.8, 0.3],\n          } : {}}\n          transition={{\n            duration: 3 + Math.random() * 4,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: Math.random() * 5,\n          }}\n        />\n      ))}\n    </div>\n  )\n}\n\nexport function InteractiveBlobBackground() {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({\n        x: (e.clientX / window.innerWidth) * 100,\n        y: (e.clientY / window.innerHeight) * 100,\n      })\n    }\n\n    window.addEventListener(\"mousemove\", handleMouseMove)\n    return () => window.removeEventListener(\"mousemove\", handleMouseMove)\n  }, [])\n\n  return (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n      {/* Main interactive blob that follows mouse */}\n      <motion.div\n        className=\"absolute w-96 h-96 rounded-full opacity-5 bg-gradient-radial from-primary-500 to-transparent\"\n        animate={{\n          x: mousePosition.x + \"%\",\n          y: mousePosition.y + \"%\",\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 50,\n          damping: 30,\n        }}\n        style={{\n          transform: \"translate(-50%, -50%)\",\n        }}\n      />\n      \n      {/* Secondary blobs with delayed following */}\n      <motion.div\n        className=\"absolute w-64 h-64 rounded-full opacity-3 bg-gradient-radial from-primary-400 to-transparent\"\n        animate={{\n          x: mousePosition.x * 0.8 + \"%\",\n          y: mousePosition.y * 0.8 + \"%\",\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 30,\n          damping: 40,\n        }}\n        style={{\n          transform: \"translate(-50%, -50%)\",\n        }}\n      />\n      \n      <motion.div\n        className=\"absolute w-48 h-48 rounded-full opacity-2 bg-gradient-radial from-primary-300 to-transparent\"\n        animate={{\n          x: mousePosition.x * 0.6 + \"%\",\n          y: mousePosition.y * 0.6 + \"%\",\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 20,\n          damping: 50,\n        }}\n        style={{\n          transform: \"translate(-50%, -50%)\",\n        }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAWO,SAAS,eAAe,EAAE,YAAY,EAAE,EAAE,QAAQ,CAAC,EAAE,WAAW,IAAI,EAAuB;IAChG,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4E,EAAE;IAE/G,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,OAAO,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAM,GAAG,CAAC,GAAG,IAAM,CAAC;oBAC9C,IAAI;oBACJ,GAAG,KAAK,MAAM,KAAK;oBACnB,GAAG,KAAK,MAAM,KAAK;oBACnB,MAAM,MAAM,KAAK,MAAM,KAAK;oBAC5B,OAAO,KAAK,MAAM,KAAK;gBACzB,CAAC;QACH;QAEA,SAAS;IACX,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;YAChF,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;wBAClB,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;wBACjB,OAAO,KAAK,IAAI;wBAChB,QAAQ,KAAK,IAAI;wBACjB,WAAW;oBACb;oBACA,SAAS,WAAW;wBAClB,GAAG;4BAAC;4BAAG;4BAAI,CAAC;4BAAI;yBAAE;wBAClB,GAAG;4BAAC;4BAAG,CAAC;4BAAI;4BAAI;yBAAE;wBAClB,OAAO;4BAAC;4BAAG;4BAAK;4BAAK;yBAAE;wBACvB,QAAQ;4BAAC;4BAAG;4BAAI;4BAAK;4BAAK;yBAAI;oBAChC,IAAI,CAAC;oBACL,YAAY;wBACV,UAAU,KAAK,KAAK,MAAM,KAAK;wBAC/B,QAAQ;wBACR,MAAM;wBACN,OAAO,KAAK,KAAK;oBACnB;mBApBK,KAAK,EAAE;;;;;YAyBf,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wBAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oBAChC;oBACA,SAAS,WAAW;wBAClB,GAAG;4BAAC;4BAAG,CAAC;4BAAK;yBAAE;wBACf,SAAS;4BAAC;4BAAK;4BAAK;yBAAI;oBAC1B,IAAI,CAAC;oBACL,YAAY;wBACV,UAAU,IAAI,KAAK,MAAM,KAAK;wBAC9B,QAAQ;wBACR,MAAM;wBACN,OAAO,KAAK,MAAM,KAAK;oBACzB;mBAfK,CAAC,SAAS,EAAE,GAAG;;;;;;;;;;;AAoB9B;AAEO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,iBAAiB;gBACf,GAAG,AAAC,EAAE,OAAO,GAAG,OAAO,UAAU,GAAI;gBACrC,GAAG,AAAC,EAAE,OAAO,GAAG,OAAO,WAAW,GAAI;YACxC;QACF;QAEA,OAAO,gBAAgB,CAAC,aAAa;QACrC,OAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;IACvD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,cAAc,CAAC,GAAG;oBACrB,GAAG,cAAc,CAAC,GAAG;gBACvB;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;gBACA,OAAO;oBACL,WAAW;gBACb;;;;;;0BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,cAAc,CAAC,GAAG,MAAM;oBAC3B,GAAG,cAAc,CAAC,GAAG,MAAM;gBAC7B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;gBACA,OAAO;oBACL,WAAW;gBACb;;;;;;0BAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,cAAc,CAAC,GAAG,MAAM;oBAC3B,GAAG,cAAc,CAAC,GAAG,MAAM;gBAC7B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;gBACA,OAAO;oBACL,WAAW;gBACb;;;;;;;;;;;;AAIR", "debugId": null}}]}
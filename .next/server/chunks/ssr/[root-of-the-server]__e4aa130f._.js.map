{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/orbitron_5ca61691.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"orbitron_5ca61691-module__6Q6cIq__className\",\n  \"variable\": \"orbitron_5ca61691-module__6Q6cIq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/orbitron_5ca61691.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Orbitron%22,%22arguments%22:[{%22variable%22:%22--font-orbitron%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22,%22800%22,%22900%22]}],%22variableName%22:%22orbitron%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Orbitron', 'Orbitron Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/share_tech_mono_47be315f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"share_tech_mono_47be315f-module__QSln4a__className\",\n  \"variable\": \"share_tech_mono_47be315f-module__QSln4a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/share_tech_mono_47be315f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Share_Tech_Mono%22,%22arguments%22:[{%22variable%22:%22--font-share-tech%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22]}],%22variableName%22:%22shareTechMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Share Tech Mono', 'Share Tech Mono Fallback'\",\n        fontWeight: 400,\nfontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,+JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,YAAY;QACpB,WAAW;IAEP;AACJ;AAEA,IAAI,+JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,+JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/titillium_web_c78ac215.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"titillium_web_c78ac215-module__WGs5HG__className\",\n  \"variable\": \"titillium_web_c78ac215-module__WGs5HG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/titillium_web_c78ac215.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Titillium_Web%22,%22arguments%22:[{%22variable%22:%22--font-titillium%22,%22subsets%22:[%22latin%22],%22weight%22:[%22300%22,%22400%22,%22600%22,%22700%22]}],%22variableName%22:%22titilliumWeb%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Titillium Web', 'Titillium Web Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,6JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,6JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,6JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/layout/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/navigation.tsx <module evaluation>\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,sEACA", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/layout/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/navigation.tsx\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,kDACA", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx <module evaluation>\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/animations/blob-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlobBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlobBackground() from the server but BlobBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/animations/blob-background.tsx <module evaluation>\",\n    \"BlobBackground\",\n);\nexport const InteractiveBlobBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call InteractiveBlobBackground() from the server but InteractiveBlobBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/animations/blob-background.tsx <module evaluation>\",\n    \"InteractiveBlobBackground\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+EACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,+EACA", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/components/animations/blob-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlobBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlobBackground() from the server but BlobBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/animations/blob-background.tsx\",\n    \"BlobBackground\",\n);\nexport const InteractiveBlobBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call InteractiveBlobBackground() from the server but InteractiveBlobBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/animations/blob-background.tsx\",\n    \"InteractiveBlobBackground\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2DACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,2DACA", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Or<PERSON>ron, Share_Tech_Mono, Titillium_Web } from \"next/font/google\";\nimport \"./globals.css\";\nimport { Navigation } from \"@/components/layout/navigation\";\nimport { Footer } from \"@/components/layout/footer\";\nimport { InteractiveBlobBackground } from \"@/components/animations/blob-background\";\n\nconst orbitron = Orbitron({\n  variable: \"--font-orbitron\",\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"600\", \"700\", \"800\", \"900\"],\n});\n\nconst shareTechMono = Share_Tech_Mono({\n  variable: \"--font-share-tech\",\n  subsets: [\"latin\"],\n  weight: [\"400\"],\n});\n\nconst titilliumWeb = Titillium_Web({\n  variable: \"--font-titillium\",\n  subsets: [\"latin\"],\n  weight: [\"300\", \"400\", \"600\", \"700\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"BuildLabz - Web3 Development Studio\",\n  description: \"We forge the future of Web3. One block at a time. From idea to reality in weeks, not months.\",\n  keywords: [\"Web3\", \"Blockchain\", \"DApp\", \"Smart Contracts\", \"Development\", \"BuildLabz\"],\n  authors: [{ name: \"BuildLabz Team\" }],\n  creator: \"BuildLabz\",\n  publisher: \"BuildLabz\",\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: \"https://buildlabz.com\",\n    title: \"BuildLabz - Web3 Development Studio\",\n    description: \"We forge the future of Web3. One block at a time.\",\n    siteName: \"BuildLabz\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"BuildLabz - Web3 Development Studio\",\n    description: \"We forge the future of Web3. One block at a time.\",\n    creator: \"@buildlabz\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className=\"dark\">\n      <body\n        className={`${orbitron.variable} ${shareTechMono.variable} ${titilliumWeb.variable} antialiased min-h-screen bg-background text-foreground`}\n      >\n        <div className=\"relative min-h-screen flex flex-col\">\n          <InteractiveBlobBackground />\n          <Navigation />\n          <main className=\"flex-1 pt-16\">\n            {children}\n          </main>\n          <Footer />\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAGA;AACA;AACA;;;;;;;;;AAoBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAQ;QAAc;QAAQ;QAAmB;QAAe;KAAY;IACvF,SAAS;QAAC;YAAE,MAAM;QAAiB;KAAE;IACrC,SAAS;IACT,WAAW;IACX,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;IACX;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;kBACxB,cAAA,8OAAC;YACC,WAAW,GAAG,4IAAA,CAAA,UAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,mJAAA,CAAA,UAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,uDAAuD,CAAC;sBAE3I,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sJAAA,CAAA,4BAAyB;;;;;kCAC1B,8OAAC,0IAAA,CAAA,aAAU;;;;;kCACX,8OAAC;wBAAK,WAAU;kCACb;;;;;;kCAEH,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/labs/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}
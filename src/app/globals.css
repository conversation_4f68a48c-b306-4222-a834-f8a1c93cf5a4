@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Share+Tech+Mono&family=Titillium+Web:wght@300;400;600;700&display=swap');

:root {
  --background: #000000;
  --foreground: #ffffff;
  --accent: #00FF9E;
  --text-secondary: #cfcfcf;
  --dark-green-deep: #0c2f27;
  --dark-green-muted: #1a4035;
  --dark-green-dark: #295247;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Titillium Web', sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-green-deep);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00cc7e;
}

/* Glow effects */
.glow-accent {
  box-shadow: 0 0 10px var(--accent), 0 0 20px var(--accent), 0 0 30px var(--accent);
}

.glow-accent-sm {
  box-shadow: 0 0 5px var(--accent), 0 0 10px var(--accent);
}

/* Text glow */
.text-glow {
  text-shadow: 0 0 10px var(--accent), 0 0 20px var(--accent);
}

/* Glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Grid background */
.grid-bg {
  background-image:
    linear-gradient(rgba(0, 255, 158, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 158, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Blob animations */
.blob {
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  animation: blob 7s infinite;
}

/* Magnetic button effect */
.magnetic {
  transition: transform 0.3s ease;
}

.magnetic:hover {
  transform: scale(1.05);
}

/* Ripple effect */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(0, 255, 158, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}

"use client"

import { motion } from "framer-motion"
import { Target, Lightbulb, Users, Zap, CheckCircle, ArrowRight } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { BlobBackground } from "@/components/animations/blob-background"
import { staggerContainer, staggerItem } from "@/lib/utils"

const values = [
  {
    icon: Target,
    title: "Mission-Driven",
    description: "We're committed to democratizing Web3 technology and making blockchain accessible to everyone.",
  },
  {
    icon: Lightbulb,
    title: "Innovation First",
    description: "We stay at the forefront of technology, constantly exploring new possibilities in the Web3 space.",
  },
  {
    icon: Users,
    title: "Client-Centric",
    description: "Your success is our success. We work closely with clients to understand and exceed their expectations.",
  },
  {
    icon: Zap,
    title: "Speed & Quality",
    description: "We deliver high-quality solutions quickly without compromising on security or performance.",
  },
]

const timeline = [
  {
    year: "2021",
    title: "The Beginning",
    description: "Founded by blockchain enthusiasts with a vision to make Web3 development accessible.",
    milestone: "Company Founded",
  },
  {
    year: "2022",
    title: "First Major Success",
    description: "Delivered our first DeFi platform that reached $10M TVL within 3 months of launch.",
    milestone: "10+ Projects Completed",
  },
  {
    year: "2023",
    title: "Team Expansion",
    description: "Grew our team to include specialists in gaming, UI/UX, and smart contract security.",
    milestone: "Team of 6 Experts",
  },
  {
    year: "2024",
    title: "Industry Recognition",
    description: "Recognized as a leading Web3 development studio with 50+ successful projects.",
    milestone: "50+ Projects Delivered",
  },
]

const techStack = [
  "React", "Next.js", "TypeScript", "Solidity", "Hardhat", "Web3.js",
  "Ethers.js", "IPFS", "Polygon", "Ethereum", "Unity", "Node.js",
  "MongoDB", "Redis", "Docker", "AWS", "Figma", "Framer Motion"
]

export default function AboutPage() {
  return (
    <div className="min-h-screen py-20">
      <BlobBackground count={3} className="opacity-20" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl md:text-6xl font-bold font-orbitron mb-6">
            About <span className="text-primary-500">BuildLabz</span>
          </h1>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto">
            We are a passionate team of Web3 developers, designers, and innovators
            dedicated to building the decentralized future, one block at a time.
          </p>
        </motion.div>

        {/* Mission & Vision */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card variant="blob" className="p-8">
            <CardHeader className="px-0 pt-0">
              <CardTitle className="text-3xl font-orbitron text-primary-500 mb-4">
                Our Mission
              </CardTitle>
            </CardHeader>
            <CardContent className="px-0">
              <p className="text-text-secondary text-lg leading-relaxed">
                To democratize Web3 technology by creating innovative, secure, and user-friendly
                blockchain solutions that empower businesses and individuals to participate in
                the decentralized economy. We believe in making complex technology simple and
                accessible to everyone.
              </p>
            </CardContent>
          </Card>

          <Card variant="blob" className="p-8">
            <CardHeader className="px-0 pt-0">
              <CardTitle className="text-3xl font-orbitron text-primary-500 mb-4">
                Our Vision
              </CardTitle>
            </CardHeader>
            <CardContent className="px-0">
              <p className="text-text-secondary text-lg leading-relaxed">
                To be the leading Web3 development studio that bridges the gap between
                traditional businesses and blockchain technology. We envision a world where
                decentralized applications are as common and easy to use as traditional
                web applications.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Values */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold font-orbitron text-center mb-12">
            Our <span className="text-primary-500">Values</span>
          </h2>
          
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {values.map((value, index) => (
              <motion.div key={value.title} variants={staggerItem}>
                <Card variant="glass" className="p-6 h-full text-center group hover:border-primary-500 transition-all duration-300">
                  <div className="mb-4">
                    <div className="w-16 h-16 bg-primary-500/20 rounded-full flex items-center justify-center mx-auto group-hover:bg-primary-500/30 transition-colors duration-300">
                      <value.icon className="w-8 h-8 text-primary-500" />
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold font-orbitron mb-3 group-hover:text-primary-500 transition-colors duration-300">
                    {value.title}
                  </h3>
                  <p className="text-text-secondary text-sm leading-relaxed">
                    {value.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Timeline */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold font-orbitron text-center mb-12">
            Our <span className="text-primary-500">Journey</span>
          </h2>
          
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-primary-500/30" />
            
            <div className="space-y-12">
              {timeline.map((item, index) => (
                <motion.div
                  key={item.year}
                  className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <Card variant="glass" className="p-6">
                      <div className="text-primary-500 font-bold text-2xl font-orbitron mb-2">
                        {item.year}
                      </div>
                      <h3 className="text-xl font-semibold font-orbitron mb-2">
                        {item.title}
                      </h3>
                      <p className="text-text-secondary mb-3">
                        {item.description}
                      </p>
                      <div className="inline-flex items-center text-sm text-primary-500 font-semibold">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        {item.milestone}
                      </div>
                    </Card>
                  </div>
                  
                  {/* Timeline Dot */}
                  <div className="relative z-10">
                    <div className="w-4 h-4 bg-primary-500 rounded-full border-4 border-background" />
                  </div>
                  
                  <div className="w-1/2" />
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Tech Stack */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold font-orbitron text-center mb-12">
            Our <span className="text-primary-500">Tech Stack</span>
          </h2>
          
          <Card variant="glass" className="p-8">
            <div className="flex flex-wrap gap-3 justify-center">
              {techStack.map((tech, index) => (
                <motion.span
                  key={tech}
                  className="px-4 py-2 bg-dark-green-deep border border-primary-500/30 rounded-full text-primary-500 font-medium hover:border-primary-500 hover:glow-accent-sm transition-all duration-300 cursor-default"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                >
                  {tech}
                </motion.span>
              ))}
            </div>
          </Card>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="bg-dark-green-deep/50 backdrop-blur-sm border border-primary-500/20 rounded-2xl p-12 max-w-4xl mx-auto">
            <h3 className="text-3xl md:text-4xl font-bold font-orbitron mb-4">
              Ready to build the future together?
            </h3>
            <p className="text-text-secondary mb-8 max-w-2xl mx-auto text-lg">
              Join us on our mission to democratize Web3 technology.
              Let's create something extraordinary that will shape the decentralized future.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="magnetic" size="xl">
                Start Your Project
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button variant="outline" size="xl">
                Learn More About Us
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

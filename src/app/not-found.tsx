"use client"

import { motion } from "framer-motion"
import { <PERSON>, ArrowLeft, Zap, AlertTriangle } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { BlobBackground } from "@/components/animations/blob-background"

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      <BlobBackground count={2} className="opacity-20" />
      
      {/* Grid background */}
      <div className="absolute inset-0 grid-bg opacity-10" />
      
      {/* Floating sparks */}
      {Array.from({ length: 10 }).map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-1 h-1 bg-primary-500 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            opacity: [0, 1, 0],
            scale: [0, 1, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: Math.random() * 2,
          }}
        />
      ))}

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Frankenstein Robot Illustration */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <div className="relative inline-block">
            {/* Robot Body */}
            <div className="w-32 h-40 bg-dark-green-deep border-2 border-primary-500 rounded-lg mx-auto relative">
              {/* Robot Head */}
              <div className="w-24 h-24 bg-dark-green-muted border-2 border-primary-500 rounded-lg absolute -top-12 left-1/2 transform -translate-x-1/2">
                {/* Eyes */}
                <div className="flex justify-center space-x-3 mt-4">
                  <motion.div
                    className="w-3 h-3 bg-primary-500 rounded-full"
                    animate={{ opacity: [1, 0.3, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                  <motion.div
                    className="w-3 h-3 bg-red-500 rounded-full"
                    animate={{ opacity: [0.3, 1, 0.3] }}
                    transition={{ duration: 2, repeat: Infinity, delay: 1 }}
                  />
                </div>
                {/* Mouth */}
                <div className="w-8 h-1 bg-primary-500 rounded-full mx-auto mt-3" />
                
                {/* Antenna */}
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="w-0.5 h-4 bg-primary-500" />
                  <motion.div
                    className="w-2 h-2 bg-primary-500 rounded-full -mt-1 mx-auto"
                    animate={{ scale: [1, 1.5, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                </div>
              </div>

              {/* Body Details */}
              <div className="pt-16 px-4">
                <div className="w-full h-2 bg-primary-500/30 rounded mb-2" />
                <div className="w-3/4 h-2 bg-primary-500/30 rounded mx-auto mb-2" />
                <div className="w-1/2 h-2 bg-primary-500/30 rounded mx-auto" />
              </div>

              {/* Arms */}
              <div className="absolute top-20 -left-6 w-6 h-12 bg-dark-green-muted border-2 border-primary-500 rounded" />
              <div className="absolute top-20 -right-6 w-6 h-12 bg-dark-green-muted border-2 border-primary-500 rounded" />
              
              {/* Holding wires */}
              <motion.div
                className="absolute top-24 -right-8"
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <div className="w-8 h-0.5 bg-primary-500" />
                <div className="w-6 h-0.5 bg-red-500 mt-1" />
                <div className="w-4 h-0.5 bg-blue-500 mt-1" />
              </motion.div>
            </div>

            {/* Sparks */}
            <motion.div
              className="absolute top-0 right-0"
              animate={{
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: 0.5,
                repeat: Infinity,
                repeatDelay: 2,
              }}
            >
              <Zap className="w-6 h-6 text-primary-500" />
            </motion.div>
          </div>
        </motion.div>

        {/* Error Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <h1 className="text-6xl md:text-8xl font-bold font-orbitron mb-4">
            <span className="text-primary-500">4</span>
            <span className="text-red-500">0</span>
            <span className="text-primary-500">4</span>
          </h1>
          
          <h2 className="text-2xl md:text-3xl font-bold font-orbitron mb-4">
            Page Short-Circuited
          </h2>
          
          <p className="text-lg text-text-secondary mb-8 max-w-2xl mx-auto">
            Looks like our Frankenstein robot is having some technical difficulties. 
            The page you're looking for seems to have been zapped out of existence!
          </p>

          <motion.div
            className="flex items-center justify-center space-x-2 mb-8"
            animate={{ opacity: [1, 0.5, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <AlertTriangle className="w-5 h-5 text-yellow-500" />
            <span className="text-yellow-500 font-mono text-sm">
              ERROR: Page not found in the lab database
            </span>
          </motion.div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button variant="magnetic" size="lg" asChild>
              <Link href="/">
                <Home className="w-5 h-5 mr-2" />
                Back to Lab
              </Link>
            </Button>
            
            <Button variant="outline" size="lg" onClick={() => window.history.back()}>
              <ArrowLeft className="w-5 h-5 mr-2" />
              Go Back
            </Button>
          </div>

          {/* Fun fact */}
          <motion.div
            className="mt-12 p-4 bg-dark-green-deep/50 backdrop-blur-sm border border-primary-500/20 rounded-lg max-w-md mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <p className="text-sm text-text-secondary">
              <span className="text-primary-500 font-semibold">Fun Fact:</span> While you're here, 
              our robot is busy fixing the circuits. Maybe check out our{" "}
              <Link href="/projects" className="text-primary-500 hover:underline">
                latest projects
              </Link>{" "}
              instead?
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}

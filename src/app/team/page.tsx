"use client"

import { motion } from "framer-motion"
import { Github, Linkedin, Twitter, Mail } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>bBackground } from "@/components/animations/blob-background"
import { staggerContainer, staggerItem } from "@/lib/utils"

const teamMembers = [
  {
    name: "<PERSON>",
    role: "Founder & CEO",
    bio: "Former blockchain architect at ConsenSys with 8+ years in Web3 development. Led teams building DeFi protocols handling $100M+ TVL.",
    image: "/api/placeholder/300/300",
    skills: ["Blockchain Architecture", "Team Leadership", "Product Strategy"],
    social: {
      github: "https://github.com/alexchen",
      linkedin: "https://linkedin.com/in/alexchen",
      twitter: "https://twitter.com/alexchen",
    }
  },
  {
    name: "<PERSON>",
    role: "<PERSON><PERSON> & Co-founder",
    bio: "Full-stack developer and smart contract security expert. Previously at OpenZeppelin, audited 200+ smart contracts.",
    image: "/api/placeholder/300/300",
    skills: ["Smart Contracts", "Security Audits", "Full-stack Development"],
    social: {
      github: "https://github.com/sarahrodriguez",
      linkedin: "https://linkedin.com/in/sarahrodriguez",
      twitter: "https://twitter.com/sarahrodriguez",
    }
  },
  {
    name: "<PERSON>",
    role: "Lead Frontend Developer",
    bio: "React specialist with expertise in Web3 integrations. Built user interfaces for top DeFi protocols with millions of users.",
    image: "/api/placeholder/300/300",
    skills: ["React/Next.js", "Web3 Integration", "UI/UX Design"],
    social: {
      github: "https://github.com/marcusjohnson",
      linkedin: "https://linkedin.com/in/marcusjohnson",
    }
  },
  {
    name: "Priya Patel",
    role: "Smart Contract Developer",
    bio: "Solidity expert with deep knowledge of DeFi protocols. Specialized in gas optimization and cross-chain development.",
    image: "/api/placeholder/300/300",
    skills: ["Solidity", "DeFi Protocols", "Gas Optimization"],
    social: {
      github: "https://github.com/priyapatel",
      linkedin: "https://linkedin.com/in/priyapatel",
      twitter: "https://twitter.com/priyapatel",
    }
  },
  {
    name: "David Kim",
    role: "Game Developer",
    bio: "Unity expert and blockchain gaming pioneer. Created multiple successful P2E games with innovative tokenomics.",
    image: "/api/placeholder/300/300",
    skills: ["Unity", "Game Design", "Tokenomics"],
    social: {
      github: "https://github.com/davidkim",
      linkedin: "https://linkedin.com/in/davidkim",
    }
  },
  {
    name: "Emma Thompson",
    role: "UI/UX Designer",
    bio: "Design systems expert focused on making Web3 accessible. Previously designed interfaces for major crypto exchanges.",
    image: "/api/placeholder/300/300",
    skills: ["Design Systems", "User Research", "Accessibility"],
    social: {
      linkedin: "https://linkedin.com/in/emmathompson",
      twitter: "https://twitter.com/emmathompson",
    }
  },
]

const stats = [
  { number: "50+", label: "Projects Delivered" },
  { number: "6", label: "Team Members" },
  { number: "25+", label: "Happy Clients" },
  { number: "3", label: "Years Experience" },
]

export default function TeamPage() {
  return (
    <div className="min-h-screen py-20">
      <BlobBackground count={3} className="opacity-20" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl md:text-6xl font-bold font-orbitron mb-6">
            Meet Our <span className="text-primary-500">Team</span>
          </h1>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto">
            A passionate group of Web3 experts, designers, and developers
            dedicated to building the future of decentralized technology.
          </p>
        </motion.div>

        {/* Stats */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="text-center"
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-3xl md:text-4xl font-bold font-orbitron text-primary-500 mb-2">
                {stat.number}
              </div>
              <div className="text-text-secondary">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Team Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20"
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          {teamMembers.map((member, index) => (
            <motion.div key={member.name} variants={staggerItem}>
              <Card variant="team" hover3d className="h-full group">
                <CardHeader className="text-center">
                  {/* Avatar */}
                  <div className="relative w-32 h-32 mx-auto mb-4">
                    <div className="w-full h-full bg-gradient-to-br from-primary-500 to-primary-400 rounded-full flex items-center justify-center text-4xl font-bold text-black">
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <motion.div
                      className="absolute inset-0 rounded-full border-2 border-primary-500 opacity-0 group-hover:opacity-100"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                    />
                  </div>
                  
                  <CardTitle className="group-hover:text-primary-500 transition-colors duration-300">
                    {member.name}
                  </CardTitle>
                  <CardDescription className="text-primary-500 font-semibold">
                    {member.role}
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  <p className="text-text-secondary text-sm mb-4 leading-relaxed">
                    {member.bio}
                  </p>

                  {/* Skills */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-primary-500 mb-2">Expertise</h4>
                    <div className="flex flex-wrap gap-2">
                      {member.skills.map((skill) => (
                        <span
                          key={skill}
                          className="px-2 py-1 bg-dark-green-muted text-text-secondary rounded-md text-xs"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Social Links */}
                  <div className="flex justify-center space-x-3">
                    {member.social.github && (
                      <motion.a
                        href={member.social.github}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-text-secondary hover:text-primary-500 transition-colors duration-300"
                        whileHover={{ scale: 1.2, rotate: 5 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Github className="w-5 h-5" />
                      </motion.a>
                    )}
                    {member.social.linkedin && (
                      <motion.a
                        href={member.social.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-text-secondary hover:text-primary-500 transition-colors duration-300"
                        whileHover={{ scale: 1.2, rotate: 5 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Linkedin className="w-5 h-5" />
                      </motion.a>
                    )}
                    {member.social.twitter && (
                      <motion.a
                        href={member.social.twitter}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-text-secondary hover:text-primary-500 transition-colors duration-300"
                        whileHover={{ scale: 1.2, rotate: 5 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Twitter className="w-5 h-5" />
                      </motion.a>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Join Us Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="bg-dark-green-deep/50 backdrop-blur-sm border border-primary-500/20 rounded-2xl p-12 max-w-4xl mx-auto">
            <h3 className="text-3xl md:text-4xl font-bold font-orbitron mb-4">
              Join Our <span className="text-primary-500">Team</span>
            </h3>
            <p className="text-text-secondary mb-8 max-w-2xl mx-auto text-lg">
              We're always looking for talented individuals who share our passion
              for Web3 technology and innovation. Ready to build the future with us?
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="magnetic" size="xl">
                <Mail className="w-5 h-5 mr-2" />
                <EMAIL>
              </Button>
              <Button variant="outline" size="xl">
                View Open Positions
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

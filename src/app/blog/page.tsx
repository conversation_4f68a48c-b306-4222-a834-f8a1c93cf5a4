"use client"

import { motion } from "framer-motion"
import { <PERSON>, Clock, ArrowRight, Tag } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { B<PERSON>bBackground } from "@/components/animations/blob-background"
import { staggerContainer, staggerItem } from "@/lib/utils"

const blogPosts = [
  {
    id: 1,
    title: "The Future of DeFi: Trends to Watch in 2024",
    excerpt: "Explore the latest developments in decentralized finance and what they mean for the future of Web3.",
    content: "DeFi continues to evolve rapidly, with new protocols and innovations emerging regularly...",
    author: "<PERSON>",
    date: "2024-01-15",
    readTime: "5 min read",
    category: "DeFi",
    tags: ["DeFi", "Blockchain", "Finance", "Web3"],
    featured: true,
  },
  {
    id: 2,
    title: "Smart Contract Security: Best Practices for 2024",
    excerpt: "Learn essential security practices to protect your smart contracts from common vulnerabilities.",
    content: "Security should be the top priority when developing smart contracts...",
    author: "<PERSON>",
    date: "2024-01-10",
    readTime: "8 min read",
    category: "Security",
    tags: ["Security", "Smart Contracts", "Solidity", "Audits"],
    featured: false,
  },
  {
    id: 3,
    title: "Building User-Friendly Web3 Interfaces",
    excerpt: "How to design intuitive interfaces that make blockchain technology accessible to mainstream users.",
    content: "User experience is crucial for Web3 adoption...",
    author: "Emma Thompson",
    date: "2024-01-05",
    readTime: "6 min read",
    category: "Design",
    tags: ["UI/UX", "Web3", "Design", "User Experience"],
    featured: false,
  },
  {
    id: 4,
    title: "GameFi Revolution: Play-to-Earn Mechanics",
    excerpt: "Dive into the world of blockchain gaming and how play-to-earn is changing the gaming industry.",
    content: "GameFi represents a paradigm shift in how we think about gaming...",
    author: "David Kim",
    date: "2024-01-01",
    readTime: "7 min read",
    category: "GameFi",
    tags: ["GameFi", "Gaming", "NFT", "P2E"],
    featured: true,
  },
  {
    id: 5,
    title: "Cross-Chain Development: Connecting Multiple Blockchains",
    excerpt: "Understanding the challenges and solutions for building applications across multiple blockchain networks.",
    content: "As the blockchain ecosystem grows, interoperability becomes increasingly important...",
    author: "Priya Patel",
    date: "2023-12-28",
    readTime: "9 min read",
    category: "Development",
    tags: ["Cross-chain", "Interoperability", "Development", "Blockchain"],
    featured: false,
  },
  {
    id: 6,
    title: "NFT Marketplace Development: A Complete Guide",
    excerpt: "Step-by-step guide to building a successful NFT marketplace from concept to launch.",
    content: "NFT marketplaces have become a cornerstone of the Web3 ecosystem...",
    author: "Marcus Johnson",
    date: "2023-12-25",
    readTime: "10 min read",
    category: "NFT",
    tags: ["NFT", "Marketplace", "Development", "IPFS"],
    featured: false,
  },
]

const categories = ["All", "DeFi", "Security", "Design", "GameFi", "Development", "NFT"]

export default function BlogPage() {
  return (
    <div className="min-h-screen py-20">
      <BlobBackground count={3} className="opacity-20" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl md:text-6xl font-bold font-orbitron mb-6">
            Lab <span className="text-primary-500">Notes</span>
          </h1>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto">
            Insights, tutorials, and thoughts from our team on Web3 development,
            blockchain technology, and the future of decentralized applications.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-2 mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {categories.map((category) => (
            <Button
              key={category}
              variant="ghost"
              size="sm"
              className="hover:bg-primary-500 hover:text-black transition-all duration-300"
            >
              {category}
            </Button>
          ))}
        </motion.div>

        {/* Featured Posts */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <h2 className="text-2xl font-bold font-orbitron mb-8">
            Featured <span className="text-primary-500">Articles</span>
          </h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {blogPosts.filter(post => post.featured).map((post, index) => (
              <Card key={post.id} variant="project" className="group cursor-pointer">
                <CardHeader>
                  <div className="flex items-center justify-between mb-3">
                    <span className="px-3 py-1 bg-primary-500/20 text-primary-500 rounded-full text-sm font-medium">
                      {post.category}
                    </span>
                    <div className="flex items-center text-sm text-text-secondary">
                      <Calendar className="w-4 h-4 mr-1" />
                      {new Date(post.date).toLocaleDateString()}
                    </div>
                  </div>
                  
                  <CardTitle className="text-xl group-hover:text-primary-500 transition-colors duration-300">
                    {post.title}
                  </CardTitle>
                  
                  <CardDescription className="text-base">
                    {post.excerpt}
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-text-secondary">
                      <span>By {post.author}</span>
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {post.readTime}
                      </div>
                    </div>
                    
                    <Button variant="ghost" size="sm" className="group-hover:text-primary-500">
                      Read More
                      <ArrowRight className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </motion.div>

        {/* All Posts */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          <h2 className="text-2xl font-bold font-orbitron mb-8">
            Latest <span className="text-primary-500">Posts</span>
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post, index) => (
              <motion.div key={post.id} variants={staggerItem}>
                <Card variant="glass" className="h-full group cursor-pointer hover:border-primary-500 transition-all duration-300">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-3">
                      <span className="px-2 py-1 bg-dark-green-muted text-primary-500 rounded-md text-xs font-medium">
                        {post.category}
                      </span>
                      <div className="flex items-center text-xs text-text-secondary">
                        <Calendar className="w-3 h-3 mr-1" />
                        {new Date(post.date).toLocaleDateString()}
                      </div>
                    </div>
                    
                    <CardTitle className="text-lg group-hover:text-primary-500 transition-colors duration-300 line-clamp-2">
                      {post.title}
                    </CardTitle>
                    
                    <CardDescription className="text-sm line-clamp-3">
                      {post.excerpt}
                    </CardDescription>
                  </CardHeader>

                  <CardContent>
                    {/* Tags */}
                    <div className="flex flex-wrap gap-1 mb-4">
                      {post.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-dark-green-deep text-text-secondary rounded text-xs"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-xs text-text-secondary">
                        <div>By {post.author}</div>
                        <div className="flex items-center mt-1">
                          <Clock className="w-3 h-3 mr-1" />
                          {post.readTime}
                        </div>
                      </div>
                      
                      <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <ArrowRight className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Newsletter Signup */}
        <motion.div
          className="mt-20 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="bg-dark-green-deep/50 backdrop-blur-sm border border-primary-500/20 rounded-2xl p-12 max-w-4xl mx-auto">
            <h3 className="text-3xl md:text-4xl font-bold font-orbitron mb-4">
              Stay Updated
            </h3>
            <p className="text-text-secondary mb-8 max-w-2xl mx-auto text-lg">
              Subscribe to our newsletter and get the latest Web3 insights,
              tutorials, and industry updates delivered to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 bg-dark-green-muted border border-dark-green-dark rounded-lg text-white placeholder-text-secondary focus:outline-none focus:border-primary-500 focus:glow-accent-sm"
              />
              <Button variant="magnetic">
                Subscribe
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Filter, ExternalLink, Github, Search } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BlobBackground } from "@/components/animations/blob-background"
import { staggerContainer, staggerItem } from "@/lib/utils"

const categories = ["All", "DApp", "NFT", "DeFi", "GameFi", "Bot", "Infrastructure"]

const projects = [
  {
    id: 1,
    title: "DeFi Trading Platform",
    description: "A comprehensive DeFi platform with yield farming, staking, and automated trading strategies. Built with React, Solidity, and Web3 integration.",
    category: "DeFi",
    tags: ["DeFi", "React", "Solidity", "Web3", "Yield Farming"],
    status: "Live",
    year: "2024",
    client: "CryptoTrade Inc.",
    links: {
      live: "https://example.com",
      github: "https://github.com/example",
    },
    metrics: {
      users: "10K+",
      tvl: "$2M+",
      transactions: "50K+"
    }
  },
  {
    id: 2,
    title: "NFT Marketplace",
    description: "Multi-chain NFT marketplace with advanced filtering, bidding, and royalty management. Supports Ethereum, Polygon, and BSC networks.",
    category: "NFT",
    tags: ["NFT", "Next.js", "IPFS", "Polygon", "Marketplace"],
    status: "Live",
    year: "2024",
    client: "ArtChain Gallery",
    links: {
      live: "https://example.com",
      github: "https://github.com/example",
    },
    metrics: {
      collections: "500+",
      volume: "$1.5M+",
      artists: "200+"
    }
  },
  {
    id: 3,
    title: "GameFi RPG",
    description: "Play-to-earn RPG game with NFT characters, land ownership, and token rewards. Built with Unity and blockchain integration.",
    category: "GameFi",
    tags: ["GameFi", "Unity", "P2E", "NFT", "Token Economy"],
    status: "Beta",
    year: "2024",
    client: "MetaGaming Studios",
    links: {
      live: "https://example.com",
    },
    metrics: {
      players: "5K+",
      nfts: "10K+",
      rewards: "$500K+"
    }
  },
  // Add more projects as needed
]

export default function ProjectsPage() {
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [searchTerm, setSearchTerm] = useState("")

  const filteredProjects = projects.filter(project => {
    const matchesCategory = selectedCategory === "All" || project.category === selectedCategory
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    return matchesCategory && matchesSearch
  })

  return (
    <div className="min-h-screen py-20">
      <BlobBackground count={3} className="opacity-20" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl md:text-6xl font-bold font-orbitron mb-6">
            Our <span className="text-primary-500">Projects</span>
          </h1>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto">
            Explore our portfolio of successful Web3 projects that showcase
            our expertise and innovation in blockchain development.
          </p>
        </motion.div>

        {/* Filters */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative w-full lg:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary w-5 h-5" />
              <input
                type="text"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-dark-green-deep border border-dark-green-muted rounded-lg text-white placeholder-text-secondary focus:outline-none focus:border-primary-500 focus:glow-accent-sm"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="transition-all duration-300"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          {filteredProjects.map((project, index) => (
            <motion.div key={project.id} variants={staggerItem}>
              <Card variant="project" className="h-full group">
                <CardHeader>
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <CardTitle className="text-2xl group-hover:text-primary-500 transition-colors duration-300">
                        {project.title}
                      </CardTitle>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="text-sm text-text-secondary">{project.client}</span>
                        <span className="text-sm text-text-secondary">•</span>
                        <span className="text-sm text-text-secondary">{project.year}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          project.status === 'Live' 
                            ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                            : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                        }`}>
                          {project.status}
                        </span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      {project.links.live && (
                        <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                      )}
                      {project.links.github && (
                        <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <Github className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                  <CardDescription className="text-base">
                    {project.description}
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-6">
                    {project.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-3 py-1 bg-dark-green-muted text-primary-500 rounded-full text-sm font-medium"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Metrics */}
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    {Object.entries(project.metrics).map(([key, value]) => (
                      <div key={key} className="text-center">
                        <div className="text-lg font-bold text-primary-500">{value}</div>
                        <div className="text-xs text-text-secondary capitalize">{key}</div>
                      </div>
                    ))}
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-3">
                    {project.links.live && (
                      <Button variant="outline" className="flex-1">
                        View Live
                        <ExternalLink className="w-4 h-4 ml-2" />
                      </Button>
                    )}
                    {project.links.github && (
                      <Button variant="ghost">
                        <Github className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Empty State */}
        {filteredProjects.length === 0 && (
          <motion.div
            className="text-center py-20"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-2xl font-bold font-orbitron mb-2">No projects found</h3>
            <p className="text-text-secondary">Try adjusting your search or filter criteria.</p>
          </motion.div>
        )}
      </div>
    </div>
  )
}

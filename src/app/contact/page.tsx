"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Mail, Phone, MapPin, Send, MessageSquare, Calendar } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BlobBackground } from "@/components/animations/blob-background"
import { staggerContainer, staggerItem } from "@/lib/utils"

const contactMethods = [
  {
    icon: Mail,
    title: "Email Us",
    description: "Send us an email and we'll respond within 24 hours",
    contact: "<EMAIL>",
    action: "mailto:<EMAIL>",
  },
  {
    icon: MessageSquare,
    title: "Live Chat",
    description: "Chat with our team on Telegram for instant support",
    contact: "@buildlabz_support",
    action: "https://t.me/buildlabz_support",
  },
  {
    icon: Calendar,
    title: "Schedule Call",
    description: "Book a consultation call to discuss your project",
    contact: "30-min free consultation",
    action: "https://calendly.com/buildlabz",
  },
]

const offices = [
  {
    city: "San Francisco",
    address: "123 Tech Street, Suite 100",
    zipcode: "CA 94105",
    phone: "+****************",
  },
  {
    city: "London",
    address: "456 Innovation Ave, Floor 5",
    zipcode: "EC2A 4DP",
    phone: "+44 20 7123 4567",
  },
  {
    city: "Singapore",
    address: "789 Blockchain Blvd, Level 12",
    zipcode: "018956",
    phone: "+65 6123 4567",
  },
]

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    project: "",
    budget: "",
    message: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log("Form submitted:", formData)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  return (
    <div className="min-h-screen py-20">
      <BlobBackground count={3} className="opacity-20" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl md:text-6xl font-bold font-orbitron mb-6">
            Get In <span className="text-primary-500">Touch</span>
          </h1>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto">
            Ready to start your Web3 journey? Let's discuss your project and create
            something extraordinary together.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card variant="glass" className="p-8">
              <CardHeader className="px-0 pt-0">
                <CardTitle className="text-2xl font-orbitron">
                  Start Your Project
                </CardTitle>
                <CardDescription>
                  Fill out the form below and we'll get back to you within 24 hours.
                </CardDescription>
              </CardHeader>
              
              <CardContent className="px-0">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Name *</label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 bg-dark-green-deep border border-dark-green-muted rounded-lg text-white placeholder-text-secondary focus:outline-none focus:border-primary-500 focus:glow-accent-sm"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Email *</label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 bg-dark-green-deep border border-dark-green-muted rounded-lg text-white placeholder-text-secondary focus:outline-none focus:border-primary-500 focus:glow-accent-sm"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Company</label>
                    <input
                      type="text"
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-dark-green-deep border border-dark-green-muted rounded-lg text-white placeholder-text-secondary focus:outline-none focus:border-primary-500 focus:glow-accent-sm"
                      placeholder="Your company name"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Project Type *</label>
                      <select
                        name="project"
                        value={formData.project}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 bg-dark-green-deep border border-dark-green-muted rounded-lg text-white focus:outline-none focus:border-primary-500 focus:glow-accent-sm"
                      >
                        <option value="">Select project type</option>
                        <option value="dapp">DApp Development</option>
                        <option value="smart-contracts">Smart Contracts</option>
                        <option value="telegram-bot">Telegram Bot</option>
                        <option value="game">Game Development</option>
                        <option value="design">UI/UX Design</option>
                        <option value="infrastructure">Web3 Infrastructure</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Budget Range</label>
                      <select
                        name="budget"
                        value={formData.budget}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-dark-green-deep border border-dark-green-muted rounded-lg text-white focus:outline-none focus:border-primary-500 focus:glow-accent-sm"
                      >
                        <option value="">Select budget range</option>
                        <option value="5k-15k">$5K - $15K</option>
                        <option value="15k-50k">$15K - $50K</option>
                        <option value="50k-100k">$50K - $100K</option>
                        <option value="100k+">$100K+</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Project Details *</label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={5}
                      className="w-full px-4 py-3 bg-dark-green-deep border border-dark-green-muted rounded-lg text-white placeholder-text-secondary focus:outline-none focus:border-primary-500 focus:glow-accent-sm resize-none"
                      placeholder="Tell us about your project, goals, and any specific requirements..."
                    />
                  </div>

                  <Button type="submit" variant="magnetic" size="lg" className="w-full">
                    Send Message
                    <Send className="w-5 h-5 ml-2" />
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {/* Contact Methods */}
            <div>
              <h3 className="text-2xl font-bold font-orbitron mb-6">
                Contact <span className="text-primary-500">Methods</span>
              </h3>
              <div className="space-y-4">
                {contactMethods.map((method, index) => (
                  <motion.a
                    key={method.title}
                    href={method.action}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Card variant="glass" className="p-6 hover:border-primary-500 transition-all duration-300 group">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-primary-500/20 rounded-lg group-hover:bg-primary-500/30 transition-colors duration-300">
                          <method.icon className="w-6 h-6 text-primary-500" />
                        </div>
                        <div>
                          <h4 className="font-semibold font-orbitron group-hover:text-primary-500 transition-colors duration-300">
                            {method.title}
                          </h4>
                          <p className="text-sm text-text-secondary mb-1">{method.description}</p>
                          <p className="text-primary-500 font-medium">{method.contact}</p>
                        </div>
                      </div>
                    </Card>
                  </motion.a>
                ))}
              </div>
            </div>

            {/* Office Locations */}
            <div>
              <h3 className="text-2xl font-bold font-orbitron mb-6">
                Our <span className="text-primary-500">Offices</span>
              </h3>
              <div className="space-y-4">
                {offices.map((office, index) => (
                  <Card key={office.city} variant="glass" className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-primary-500/20 rounded-lg">
                        <MapPin className="w-6 h-6 text-primary-500" />
                      </div>
                      <div>
                        <h4 className="font-semibold font-orbitron text-primary-500 mb-1">
                          {office.city}
                        </h4>
                        <p className="text-sm text-text-secondary">{office.address}</p>
                        <p className="text-sm text-text-secondary">{office.zipcode}</p>
                        <p className="text-sm text-primary-500 mt-2">{office.phone}</p>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            {/* FAQ */}
            <div>
              <h3 className="text-2xl font-bold font-orbitron mb-6">
                Quick <span className="text-primary-500">FAQ</span>
              </h3>
              <Card variant="glass" className="p-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-primary-500 mb-1">How long does a typical project take?</h4>
                    <p className="text-sm text-text-secondary">Most projects take 4-12 weeks depending on complexity.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-primary-500 mb-1">Do you provide ongoing support?</h4>
                    <p className="text-sm text-text-secondary">Yes, we offer maintenance and support packages.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-primary-500 mb-1">What's your development process?</h4>
                    <p className="text-sm text-text-secondary">We follow an agile approach with regular updates and feedback.</p>
                  </div>
                </div>
              </Card>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

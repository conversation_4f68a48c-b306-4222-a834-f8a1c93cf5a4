import type { <PERSON>ada<PERSON> } from "next";
import { Or<PERSON>ron, Share_Tech_Mono, Titillium_Web } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/layout/navigation";
import { Footer } from "@/components/layout/footer";
import { InteractiveBlobBackground } from "@/components/animations/blob-background";

const orbitron = Orbitron({
  variable: "--font-orbitron",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800", "900"],
});

const shareTechMono = Share_Tech_Mono({
  variable: "--font-share-tech",
  subsets: ["latin"],
  weight: ["400"],
});

const titilliumWeb = Titillium_Web({
  variable: "--font-titillium",
  subsets: ["latin"],
  weight: ["300", "400", "600", "700"],
});

export const metadata: Metadata = {
  title: "BuildLabz - Web3 Development Studio",
  description: "We forge the future of Web3. One block at a time. From idea to reality in weeks, not months.",
  keywords: ["Web3", "Blockchain", "DApp", "Smart Contracts", "Development", "BuildLabz"],
  authors: [{ name: "BuildLabz Team" }],
  creator: "BuildLabz",
  publisher: "BuildLabz",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://buildlabz.com",
    title: "BuildLabz - Web3 Development Studio",
    description: "We forge the future of Web3. One block at a time.",
    siteName: "BuildLabz",
  },
  twitter: {
    card: "summary_large_image",
    title: "BuildLabz - Web3 Development Studio",
    description: "We forge the future of Web3. One block at a time.",
    creator: "@buildlabz",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${orbitron.variable} ${shareTechMono.variable} ${titilliumWeb.variable} antialiased min-h-screen bg-background text-foreground`}
      >
        <div className="relative min-h-screen flex flex-col">
          <InteractiveBlobBackground />
          <Navigation />
          <main className="flex-1 pt-16">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}

"use client"

import { motion } from "framer-motion"
import { Code, Smartphone, Gamepad2, Palette, Shield, Zap, CheckCircle, ArrowRight } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BlobBackground } from "@/components/animations/blob-background"
import { staggerContainer, staggerItem } from "@/lib/utils"

const services = [
  {
    icon: Code,
    title: "DApp Development",
    description: "Full-stack decentralized applications built with cutting-edge Web3 technologies.",
    features: [
      "React/Next.js Frontend Development",
      "Smart Contract Integration",
      "Web3 Wallet Connection",
      "IPFS Storage Solutions",
      "Multi-chain Support",
      "Responsive Design"
    ],
    technologies: ["React", "Next.js", "Web3.js", "Ethers.js", "IPFS", "MetaMask"],
    pricing: "Starting from $15,000",
    timeline: "4-8 weeks",
    color: "from-primary-500 to-primary-400",
  },
  {
    icon: Shield,
    title: "Smart Contracts",
    description: "Secure, audited smart contracts for Ethereum, Polygon, and other EVM chains.",
    features: [
      "Solidity Development",
      "Security Audits",
      "Gas Optimization",
      "Multi-chain Deployment",
      "Upgradeable Contracts",
      "Testing & Documentation"
    ],
    technologies: ["Solidity", "Hardhat", "OpenZeppelin", "Foundry", "Slither", "MythX"],
    pricing: "Starting from $8,000",
    timeline: "2-4 weeks",
    color: "from-blue-500 to-blue-400",
  },
  {
    icon: Smartphone,
    title: "Telegram Bots",
    description: "Intelligent bots for community management, trading, and Web3 interactions.",
    features: [
      "Trading Bots",
      "Community Management",
      "NFT Alerts & Monitoring",
      "DeFi Integration",
      "Custom Commands",
      "Analytics Dashboard"
    ],
    technologies: ["Node.js", "Telegram API", "Web3", "MongoDB", "Redis", "Docker"],
    pricing: "Starting from $3,000",
    timeline: "1-3 weeks",
    color: "from-purple-500 to-purple-400",
  },
  {
    icon: Gamepad2,
    title: "Game Development",
    description: "Blockchain games with NFT integration and play-to-earn mechanics.",
    features: [
      "Unity Game Development",
      "NFT Marketplace Integration",
      "Token Economics Design",
      "Multi-platform Support",
      "Play-to-Earn Mechanics",
      "In-game Asset Trading"
    ],
    technologies: ["Unity", "C#", "Solidity", "IPFS", "Polygon", "Moralis"],
    pricing: "Starting from $25,000",
    timeline: "8-16 weeks",
    color: "from-orange-500 to-orange-400",
  },
  {
    icon: Palette,
    title: "UI/UX Design",
    description: "Modern, intuitive interfaces that make Web3 accessible to everyone.",
    features: [
      "User Research & Analysis",
      "Wireframing & Prototyping",
      "Design Systems",
      "Accessibility Compliance",
      "Mobile-first Design",
      "Usability Testing"
    ],
    technologies: ["Figma", "Adobe XD", "Framer", "Principle", "InVision", "Maze"],
    pricing: "Starting from $5,000",
    timeline: "2-6 weeks",
    color: "from-pink-500 to-pink-400",
  },
  {
    icon: Zap,
    title: "Web3 Infrastructure",
    description: "Complete infrastructure setup for your Web3 project deployment.",
    features: [
      "Node Setup & Management",
      "API Development",
      "Database Design",
      "Cloud Deployment",
      "Monitoring & Analytics",
      "Security Implementation"
    ],
    technologies: ["AWS", "Docker", "Kubernetes", "MongoDB", "Redis", "Grafana"],
    pricing: "Starting from $10,000",
    timeline: "3-6 weeks",
    color: "from-green-500 to-green-400",
  },
]

export default function ServicesPage() {
  return (
    <div className="min-h-screen py-20">
      <BlobBackground count={3} className="opacity-20" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl md:text-6xl font-bold font-orbitron mb-6">
            Our <span className="text-primary-500">Services</span>
          </h1>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto">
            From concept to deployment, we provide end-to-end Web3 development services
            that bring your blockchain vision to life.
          </p>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20"
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          {services.map((service, index) => (
            <motion.div key={service.title} variants={staggerItem}>
              <Card variant="project" className="h-full group">
                <CardHeader>
                  <div className="flex items-center space-x-4 mb-4">
                    <div className={`p-4 rounded-xl bg-gradient-to-r ${service.color} group-hover:scale-110 transition-transform duration-300`}>
                      <service.icon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-2xl group-hover:text-primary-500 transition-colors duration-300">
                        {service.title}
                      </CardTitle>
                      <div className="flex items-center space-x-4 mt-1">
                        <span className="text-sm text-primary-500 font-semibold">{service.pricing}</span>
                        <span className="text-sm text-text-secondary">•</span>
                        <span className="text-sm text-text-secondary">{service.timeline}</span>
                      </div>
                    </div>
                  </div>
                  <CardDescription className="text-base">
                    {service.description}
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold font-orbitron mb-3 text-primary-500">
                      What's Included
                    </h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm text-text-secondary">
                          <CheckCircle className="w-4 h-4 text-primary-500 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Technologies */}
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold font-orbitron mb-3 text-primary-500">
                      Technologies
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {service.technologies.map((tech) => (
                        <span
                          key={tech}
                          className="px-3 py-1 bg-dark-green-muted text-text-secondary rounded-full text-sm"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* CTA */}
                  <Button variant="outline" className="w-full group-hover:bg-primary-500 group-hover:text-black transition-all duration-300">
                    Get Started
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Process Section */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold font-orbitron text-center mb-12">
            Our <span className="text-primary-500">Process</span>
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: "01", title: "Discovery", description: "We analyze your requirements and define project scope" },
              { step: "02", title: "Design", description: "Create wireframes, prototypes, and technical architecture" },
              { step: "03", title: "Development", description: "Build your solution with regular updates and feedback" },
              { step: "04", title: "Deployment", description: "Launch your project with ongoing support and maintenance" },
            ].map((phase, index) => (
              <motion.div
                key={phase.step}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-400 rounded-full flex items-center justify-center text-black font-bold text-xl mx-auto mb-4">
                  {phase.step}
                </div>
                <h3 className="text-xl font-semibold font-orbitron mb-2">{phase.title}</h3>
                <p className="text-text-secondary">{phase.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="bg-dark-green-deep/50 backdrop-blur-sm border border-primary-500/20 rounded-2xl p-12 max-w-4xl mx-auto">
            <h3 className="text-3xl md:text-4xl font-bold font-orbitron mb-4">
              Ready to start your Web3 project?
            </h3>
            <p className="text-text-secondary mb-8 max-w-2xl mx-auto text-lg">
              Let's discuss your vision and create something extraordinary together.
              Our team is ready to turn your ideas into reality.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="magnetic" size="xl">
                Start Your Project
              </Button>
              <Button variant="outline" size="xl">
                Schedule Consultation
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

"use client"

import Link from "next/link"
import { motion } from "framer-motion"
import { Github, Twitter, Linkedin, Mail, Zap, ExternalLink } from "lucide-react"
import { Button } from "@/components/ui/button"

const footerLinks = {
  services: [
    { name: "DApp Development", href: "/services#dapp" },
    { name: "Smart Contracts", href: "/services#contracts" },
    { name: "Telegram Bots", href: "/services#bots" },
    { name: "Game Development", href: "/services#games" },
    { name: "UI/UX Design", href: "/services#design" },
  ],
  company: [
    { name: "About Us", href: "/about" },
    { name: "Our Team", href: "/team" },
    { name: "Careers", href: "/careers" },
    { name: "Blog", href: "/blog" },
    { name: "Contact", href: "/contact" },
  ],
  resources: [
    { name: "Documentation", href: "/docs" },
    { name: "Case Studies", href: "/projects" },
    { name: "Open Source", href: "/open-source" },
    { name: "Community", href: "/community" },
    { name: "Support", href: "/support" },
  ],
}

const socialLinks = [
  { name: "GitHub", icon: Github, href: "https://github.com/buildlabz" },
  { name: "Twitter", icon: Twitter, href: "https://twitter.com/buildlabz" },
  { name: "LinkedIn", icon: Linkedin, href: "https://linkedin.com/company/buildlabz" },
  { name: "Email", icon: Mail, href: "mailto:<EMAIL>" },
]

export function Footer() {
  return (
    <footer className="relative bg-dark-green-deep border-t border-dark-green-muted">
      {/* Grid background */}
      <div className="absolute inset-0 grid-bg opacity-20" />
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <motion.div
              className="flex items-center space-x-2 mb-4"
              whileHover={{ scale: 1.05 }}
            >
              <div className="relative">
                <Zap className="w-8 h-8 text-primary-500" />
                <motion.div
                  className="absolute inset-0 w-8 h-8 text-primary-500"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                >
                  <Zap className="w-8 h-8" />
                </motion.div>
              </div>
              <span className="text-2xl font-bold font-orbitron text-glow">
                BuildLabz
              </span>
            </motion.div>
            
            <p className="text-text-secondary mb-6 max-w-md">
              We forge the future of Web3. One block at a time. From idea to reality in weeks, not months.
            </p>
            
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-text-secondary hover:text-primary-500 transition-colors duration-300"
                  whileHover={{ scale: 1.2, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <social.icon className="w-5 h-5" />
                  <span className="sr-only">{social.name}</span>
                </motion.a>
              ))}
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold font-orbitron text-primary-500 mb-4">
              Services
            </h3>
            <ul className="space-y-2">
              {footerLinks.services.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-text-secondary hover:text-primary-500 transition-colors duration-300 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-lg font-semibold font-orbitron text-primary-500 mb-4">
              Company
            </h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-text-secondary hover:text-primary-500 transition-colors duration-300 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-lg font-semibold font-orbitron text-primary-500 mb-4">
              Resources
            </h3>
            <ul className="space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-text-secondary hover:text-primary-500 transition-colors duration-300 text-sm flex items-center"
                  >
                    {link.name}
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Newsletter Signup */}
        <motion.div
          className="mt-12 pt-8 border-t border-dark-green-muted"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0">
              <h3 className="text-lg font-semibold font-orbitron text-primary-500 mb-2">
                Stay Updated
              </h3>
              <p className="text-text-secondary text-sm">
                Get the latest updates on Web3 development and BuildLabz news.
              </p>
            </div>
            <div className="flex space-x-2">
              <input
                type="email"
                placeholder="Enter your email"
                className="px-4 py-2 bg-dark-green-muted border border-dark-green-dark rounded-md text-white placeholder-text-secondary focus:outline-none focus:border-primary-500 focus:glow-accent-sm"
              />
              <Button variant="magnetic" size="sm">
                Subscribe
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Bottom Bar */}
        <div className="mt-8 pt-8 border-t border-dark-green-muted flex flex-col md:flex-row items-center justify-between">
          <p className="text-text-secondary text-sm">
            © 2024 BuildLabz. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link href="/privacy" className="text-text-secondary hover:text-primary-500 text-sm transition-colors duration-300">
              Privacy Policy
            </Link>
            <Link href="/terms" className="text-text-secondary hover:text-primary-500 text-sm transition-colors duration-300">
              Terms of Service
            </Link>
            <Link href="/cookies" className="text-text-secondary hover:text-primary-500 text-sm transition-colors duration-300">
              Cookie Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}

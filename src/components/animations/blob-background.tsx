"use client"

import { motion } from "framer-motion"
import { useEffect, useState } from "react"

interface BlobBackgroundProps {
  className?: string
  count?: number
  animated?: boolean
}

export function BlobBackground({ className = "", count = 3, animated = true }: BlobBackgroundProps) {
  const [blobs, setBlobs] = useState<Array<{ id: number; x: number; y: number; size: number; delay: number }>>([])

  useEffect(() => {
    const generateBlobs = () => {
      return Array.from({ length: count }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: 200 + Math.random() * 300,
        delay: Math.random() * 5,
      }))
    }

    setBlobs(generateBlobs())
  }, [count])

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {blobs.map((blob) => (
        <motion.div
          key={blob.id}
          className="absolute rounded-full opacity-10 bg-gradient-to-br from-primary-500 to-primary-300"
          style={{
            left: `${blob.x}%`,
            top: `${blob.y}%`,
            width: blob.size,
            height: blob.size,
            transform: "translate(-50%, -50%)",
          }}
          animate={animated ? {
            x: [0, 30, -30, 0],
            y: [0, -30, 30, 0],
            scale: [1, 1.1, 0.9, 1],
            rotate: [0, 90, 180, 270, 360],
          } : {}}
          transition={{
            duration: 20 + Math.random() * 10,
            repeat: Infinity,
            ease: "linear",
            delay: blob.delay,
          }}
        />
      ))}
      
      {/* Additional floating particles */}
      {Array.from({ length: 20 }).map((_, i) => (
        <motion.div
          key={`particle-${i}`}
          className="absolute w-1 h-1 bg-primary-500 rounded-full opacity-30"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={animated ? {
            y: [0, -100, 0],
            opacity: [0.3, 0.8, 0.3],
          } : {}}
          transition={{
            duration: 3 + Math.random() * 4,
            repeat: Infinity,
            ease: "easeInOut",
            delay: Math.random() * 5,
          }}
        />
      ))}
    </div>
  )
}

export function InteractiveBlobBackground() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      })
    }

    window.addEventListener("mousemove", handleMouseMove)
    return () => window.removeEventListener("mousemove", handleMouseMove)
  }, [])

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Main interactive blob that follows mouse */}
      <motion.div
        className="absolute w-96 h-96 rounded-full opacity-5 bg-gradient-radial from-primary-500 to-transparent"
        animate={{
          x: mousePosition.x + "%",
          y: mousePosition.y + "%",
        }}
        transition={{
          type: "spring",
          stiffness: 50,
          damping: 30,
        }}
        style={{
          transform: "translate(-50%, -50%)",
        }}
      />
      
      {/* Secondary blobs with delayed following */}
      <motion.div
        className="absolute w-64 h-64 rounded-full opacity-3 bg-gradient-radial from-primary-400 to-transparent"
        animate={{
          x: mousePosition.x * 0.8 + "%",
          y: mousePosition.y * 0.8 + "%",
        }}
        transition={{
          type: "spring",
          stiffness: 30,
          damping: 40,
        }}
        style={{
          transform: "translate(-50%, -50%)",
        }}
      />
      
      <motion.div
        className="absolute w-48 h-48 rounded-full opacity-2 bg-gradient-radial from-primary-300 to-transparent"
        animate={{
          x: mousePosition.x * 0.6 + "%",
          y: mousePosition.y * 0.6 + "%",
        }}
        transition={{
          type: "spring",
          stiffness: 20,
          damping: 50,
        }}
        style={{
          transform: "translate(-50%, -50%)",
        }}
      />
    </div>
  )
}

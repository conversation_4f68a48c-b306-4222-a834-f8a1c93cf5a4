import * as React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    variant?: "default" | "glass" | "project" | "team" | "blob"
    hover3d?: boolean
  }
>(({ className, variant = "default", hover3d = false, children, ...props }, ref) => {
  const baseClasses = "rounded-lg border text-card-foreground shadow-sm"
  
  const variantClasses = {
    default: "bg-dark-green-deep border-dark-green-muted",
    glass: "glass border-white/10",
    project: "bg-dark-green-deep border-dark-green-muted hover:border-primary-500 transition-all duration-300 group overflow-hidden",
    team: "bg-dark-green-deep border-dark-green-muted hover:glow-accent-sm transition-all duration-300",
    blob: "bg-gradient-to-br from-dark-green-deep to-dark-green-muted border-primary-500/20 relative overflow-hidden"
  }

  if (hover3d) {
    return (
      <motion.div
        ref={ref}
        className={cn(baseClasses, variantClasses[variant], className)}
        whileHover={{ 
          rotateX: 5, 
          rotateY: 5, 
          scale: 1.02,
          transition: { duration: 0.3 }
        }}
        style={{ 
          transformStyle: "preserve-3d",
          perspective: "1000px"
        }}
        {...props}
      >
        {variant === "blob" && (
          <div className="absolute inset-0 opacity-20">
            <div className="blob absolute top-4 right-4 w-20 h-20 bg-primary-500/30" />
            <div className="blob absolute bottom-4 left-4 w-16 h-16 bg-primary-500/20" style={{ animationDelay: "2s" }} />
          </div>
        )}
        {children}
      </motion.div>
    )
  }

  return (
    <div
      ref={ref}
      className={cn(baseClasses, variantClasses[variant], className)}
      {...props}
    >
      {variant === "blob" && (
        <div className="absolute inset-0 opacity-20">
          <div className="blob absolute top-4 right-4 w-20 h-20 bg-primary-500/30" />
          <div className="blob absolute bottom-4 left-4 w-16 h-16 bg-primary-500/20" style={{ animationDelay: "2s" }} />
        </div>
      )}
      {children}
    </div>
  )
})
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement> & {
    glow?: boolean
  }
>(({ className, glow = false, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight font-orbitron",
      glow && "text-glow",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-text-secondary", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary-500 text-black hover:bg-primary-400 glow-accent-sm hover:glow-accent font-semibold",
        destructive: "bg-red-500 text-white hover:bg-red-600",
        outline: "border border-primary-500 bg-transparent text-primary-500 hover:bg-primary-500 hover:text-black glow-accent-sm hover:glow-accent",
        secondary: "bg-dark-green-muted text-white hover:bg-dark-green-dark",
        ghost: "text-primary-500 hover:bg-dark-green-deep hover:text-primary-400 transition-all duration-300",
        link: "text-primary-500 underline-offset-4 hover:underline",
        magnetic: "bg-primary-500 text-black hover:bg-primary-400 magnetic ripple font-semibold glow-accent-sm hover:glow-accent",
        flicker: "bg-transparent border border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-black animate-flicker",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        xl: "h-14 rounded-lg px-12 text-lg",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  withSpark?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, withSpark = false, children, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    const buttonContent = (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {children}
        {withSpark && (
          <span className="absolute inset-0 overflow-hidden rounded-md">
            <span className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-primary-500/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
          </span>
        )}
      </Comp>
    )

    if (variant === "magnetic") {
      return (
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="relative group"
        >
          {buttonContent}
        </motion.div>
      )
    }

    return buttonContent
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }

"use client"

import { motion } from "framer-motion"
import { Code, Smartphone, Gamepad2, Palette, Shield, Zap } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { staggerContainer, staggerItem } from "@/lib/utils"

const services = [
  {
    icon: Code,
    title: "DApp Development",
    description: "Full-stack decentralized applications built with cutting-edge Web3 technologies.",
    features: ["React/Next.js Frontend", "Smart Contract Integration", "Web3 Wallet Connection", "IPFS Storage"],
    color: "from-primary-500 to-primary-400",
  },
  {
    icon: Shield,
    title: "Smart Contracts",
    description: "Secure, audited smart contracts for Ethereum, Polygon, and other EVM chains.",
    features: ["Solidity Development", "Security Audits", "Gas Optimization", "Multi-chain Support"],
    color: "from-blue-500 to-blue-400",
  },
  {
    icon: Smartphone,
    title: "Telegram Bo<PERSON>",
    description: "Intelligent bots for community management, trading, and Web3 interactions.",
    features: ["Trading Bots", "Community Management", "NFT Alerts", "DeFi Integration"],
    color: "from-purple-500 to-purple-400",
  },
  {
    icon: Gamepad2,
    title: "Game Development",
    description: "Blockchain games with NFT integration and play-to-earn mechanics.",
    features: ["Unity Integration", "NFT Marketplace", "Token Economics", "Multi-platform"],
    color: "from-orange-500 to-orange-400",
  },
  {
    icon: Palette,
    title: "UI/UX Design",
    description: "Modern, intuitive interfaces that make Web3 accessible to everyone.",
    features: ["User Research", "Prototyping", "Design Systems", "Accessibility"],
    color: "from-pink-500 to-pink-400",
  },
  {
    icon: Zap,
    title: "Web3 Infrastructure",
    description: "Complete infrastructure setup for your Web3 project deployment.",
    features: ["Node Setup", "API Development", "Database Design", "Cloud Deployment"],
    color: "from-green-500 to-green-400",
  },
]

export function ServicesSection() {
  return (
    <section className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-5xl font-bold font-orbitron mb-4">
            Our <span className="text-primary-500">Services</span>
          </h2>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto">
            From concept to deployment, we provide end-to-end Web3 development services
            that bring your blockchain vision to life.
          </p>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
        >
          {services.map((service, index) => (
            <motion.div key={service.title} variants={staggerItem}>
              <Card 
                variant="project" 
                hover3d 
                className="h-full group cursor-pointer"
              >
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-4">
                    <div className={`p-3 rounded-lg bg-gradient-to-r ${service.color} group-hover:scale-110 transition-transform duration-300`}>
                      <service.icon className="w-6 h-6 text-white" />
                    </div>
                    <CardTitle className="text-xl group-hover:text-primary-500 transition-colors duration-300">
                      {service.title}
                    </CardTitle>
                  </div>
                  <CardDescription className="text-base">
                    {service.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-text-secondary">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button 
                    variant="ghost" 
                    className="w-full group-hover:bg-primary-500 group-hover:text-black transition-all duration-300"
                  >
                    Learn More
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <div className="bg-dark-green-deep/50 backdrop-blur-sm border border-primary-500/20 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-bold font-orbitron mb-4">
              Ready to build the future?
            </h3>
            <p className="text-text-secondary mb-6 max-w-2xl mx-auto">
              Let's discuss your Web3 project and create something extraordinary together.
              Our team is ready to turn your vision into reality.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="magnetic" size="lg">
                Start Your Project
              </Button>
              <Button variant="outline" size="lg">
                Schedule Consultation
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

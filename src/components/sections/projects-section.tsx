"use client"

import { motion } from "framer-motion"
import { <PERSON>Link, Gith<PERSON>, ArrowRight } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { staggerContainer, staggerItem } from "@/lib/utils"

const projects = [
  {
    title: "DeFi Trading Platform",
    description: "A comprehensive DeFi platform with yield farming, staking, and automated trading strategies.",
    image: "/api/placeholder/400/250",
    tags: ["DeFi", "React", "Solidity", "Web3"],
    category: "DApp",
    status: "Live",
    links: {
      live: "https://example.com",
      github: "https://github.com/example",
    }
  },
  {
    title: "NFT Marketplace",
    description: "Multi-chain NFT marketplace with advanced filtering, bidding, and royalty management.",
    image: "/api/placeholder/400/250",
    tags: ["NFT", "Next.js", "IPFS", "Polygon"],
    category: "Marketplace",
    status: "Live",
    links: {
      live: "https://example.com",
      github: "https://github.com/example",
    }
  },
  {
    title: "GameFi RPG",
    description: "Play-to-earn RPG game with NFT characters, land ownership, and token rewards.",
    image: "/api/placeholder/400/250",
    tags: ["GameFi", "Unity", "P2E", "NFT"],
    category: "Game",
    status: "Beta",
    links: {
      live: "https://example.com",
    }
  },
  {
    title: "DAO Governance Platform",
    description: "Decentralized governance platform with proposal creation, voting, and treasury management.",
    image: "/api/placeholder/400/250",
    tags: ["DAO", "Governance", "React", "Ethereum"],
    category: "DApp",
    status: "Live",
    links: {
      live: "https://example.com",
      github: "https://github.com/example",
    }
  },
  {
    title: "Crypto Trading Bot",
    description: "Intelligent Telegram bot for automated crypto trading with advanced analytics.",
    image: "/api/placeholder/400/250",
    tags: ["Bot", "Trading", "AI", "Telegram"],
    category: "Bot",
    status: "Live",
    links: {
      live: "https://t.me/example",
    }
  },
  {
    title: "Cross-Chain Bridge",
    description: "Secure bridge for transferring assets between Ethereum, Polygon, and BSC networks.",
    image: "/api/placeholder/400/250",
    tags: ["Bridge", "Multi-chain", "Security", "DeFi"],
    category: "Infrastructure",
    status: "Live",
    links: {
      live: "https://example.com",
      github: "https://github.com/example",
    }
  },
]

export function ProjectsSection() {
  return (
    <section className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-5xl font-bold font-orbitron mb-4">
            Featured <span className="text-primary-500">Projects</span>
          </h2>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto">
            Explore our portfolio of successful Web3 projects that showcase
            our expertise and innovation in blockchain development.
          </p>
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
        >
          {projects.map((project, index) => (
            <motion.div key={project.title} variants={staggerItem}>
              <Card variant="project" hover3d className="h-full group overflow-hidden">
                {/* Project Image */}
                <div className="relative h-48 bg-gradient-to-br from-dark-green-deep to-dark-green-muted overflow-hidden">
                  <div className="absolute inset-0 bg-grid-pattern opacity-20" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-6xl font-bold font-orbitron text-primary-500/20">
                      {project.category}
                    </div>
                  </div>
                  
                  {/* Status Badge */}
                  <div className="absolute top-4 right-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      project.status === 'Live' 
                        ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                        : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                    }`}>
                      {project.status}
                    </span>
                  </div>

                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-4">
                    {project.links.live && (
                      <Button variant="ghost" size="icon" className="text-white hover:text-primary-500">
                        <ExternalLink className="w-5 h-5" />
                      </Button>
                    )}
                    {project.links.github && (
                      <Button variant="ghost" size="icon" className="text-white hover:text-primary-500">
                        <Github className="w-5 h-5" />
                      </Button>
                    )}
                  </div>
                </div>

                <CardHeader>
                  <CardTitle className="group-hover:text-primary-500 transition-colors duration-300">
                    {project.title}
                  </CardTitle>
                  <CardDescription>
                    {project.description}
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-dark-green-muted text-primary-500 rounded-md text-xs font-medium"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Links */}
                  <div className="flex space-x-2">
                    {project.links.live && (
                      <Button variant="outline" size="sm" className="flex-1">
                        View Live
                        <ExternalLink className="w-3 h-3 ml-1" />
                      </Button>
                    )}
                    {project.links.github && (
                      <Button variant="ghost" size="sm">
                        <Github className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* View All Projects CTA */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <Button variant="magnetic" size="lg" className="group">
            View All Projects
            <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
        </motion.div>
      </div>
    </section>
  )
}

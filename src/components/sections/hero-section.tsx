"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Rocket } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BlobBackground } from "@/components/animations/blob-background"
import { fadeInUp, fadeInLeft, fadeInRight, staggerContainer, staggerItem } from "@/lib/utils"

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Elements */}
      <BlobBackground count={4} className="opacity-30" />
      
      {/* Grid Background */}
      <div className="absolute inset-0 grid-bg opacity-10" />
      
      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <motion.div
          className="text-center"
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          {/* Badge */}
          <motion.div
            variants={staggerItem}
            className="inline-flex items-center space-x-2 bg-dark-green-deep/50 backdrop-blur-sm border border-primary-500/30 rounded-full px-4 py-2 mb-8"
          >
            <Zap className="w-4 h-4 text-primary-500" />
            <span className="text-sm font-medium text-primary-500">
              Web3 Development Studio
            </span>
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            variants={staggerItem}
            className="text-4xl md:text-6xl lg:text-7xl font-bold font-orbitron mb-6 leading-tight"
          >
            <span className="text-glow">We forge the</span>
            <br />
            <span className="text-primary-500">future of Web3</span>
          </motion.h1>

          {/* Tagline */}
          <motion.p
            variants={staggerItem}
            className="text-xl md:text-2xl text-text-secondary mb-8 max-w-3xl mx-auto"
          >
            One block at a time. From idea to reality in{" "}
            <span className="text-primary-500 font-semibold">weeks, not months</span>.
          </motion.p>

          {/* Feature Pills */}
          <motion.div
            variants={staggerItem}
            className="flex flex-wrap justify-center gap-4 mb-12"
          >
            {[
              { icon: Code, text: "DApp Development" },
              { icon: Zap, text: "Smart Contracts" },
              { icon: Rocket, text: "Rapid Deployment" },
            ].map((feature, index) => (
              <motion.div
                key={feature.text}
                className="flex items-center space-x-2 bg-dark-green-deep/30 backdrop-blur-sm border border-dark-green-muted rounded-full px-4 py-2"
                whileHover={{ scale: 1.05, borderColor: "#00FF9E" }}
                transition={{ duration: 0.2 }}
              >
                <feature.icon className="w-4 h-4 text-primary-500" />
                <span className="text-sm text-text-secondary">{feature.text}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            variants={staggerItem}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button variant="magnetic" size="xl" className="group">
              Get Started
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button variant="outline" size="xl">
              View Our Work
            </Button>
          </motion.div>

          {/* Stats */}
          <motion.div
            variants={staggerItem}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-20 pt-20 border-t border-dark-green-muted"
          >
            {[
              { number: "50+", label: "Projects Delivered" },
              { number: "25+", label: "Happy Clients" },
              { number: "100%", label: "Success Rate" },
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-3xl md:text-4xl font-bold font-orbitron text-primary-500 mb-2">
                  {stat.number}
                </div>
                <div className="text-text-secondary">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>

      {/* Floating Elements */}
      <motion.div
        className="absolute top-20 left-10 w-4 h-4 bg-primary-500 rounded-full opacity-60"
        animate={{
          y: [0, -20, 0],
          opacity: [0.6, 1, 0.6],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute top-40 right-20 w-2 h-2 bg-primary-500 rounded-full opacity-40"
        animate={{
          y: [0, -30, 0],
          opacity: [0.4, 0.8, 0.4],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1,
        }}
      />
      <motion.div
        className="absolute bottom-40 left-20 w-3 h-3 bg-primary-500 rounded-full opacity-50"
        animate={{
          y: [0, -25, 0],
          opacity: [0.5, 0.9, 0.5],
        }}
        transition={{
          duration: 3.5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2,
        }}
      />
    </section>
  )
}
